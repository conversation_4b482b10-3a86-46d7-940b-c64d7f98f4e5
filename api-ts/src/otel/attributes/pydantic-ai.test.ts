import { describe, it, expect } from "vitest";
import { otelSpanToRow } from "../collector";

/**
 * Test data for pydantic-ai OTEL telemetry processing.
 *
 * This data was captured from running the pydantic-ai example in examples/pydantic-ai/
 * with BRAINTRUST_OTEL_CONSOLE_EXPORT=true. The example creates an agent with two tools
 * (roll_dice and get_player_name) and runs a dice guessing game.
 *
 * The pydantic-ai library generates several types of OTEL spans:
 * 1. Chat spans (gen_ai.operation.name: "chat") - contain events as JSON string in attributes
 * 2. Agent run spans (logfire.msg: "agent run") - contain all_messages_events as JSON string
 * 3. Tool execution spans (gen_ai.tool.name + logfire.msg: "running tool:") - individual tool calls
 *
 * The test data represents the console exporter output converted to proper OTEL format.
 */

describe("pydantic-ai OTEL ingestion", () => {
  it("should process pydantic-ai chat span with events field", () => {
    // Test a chat span with gen_ai.operation.name: "chat" and events in attributes
    const pydanticAiChatSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "383402bd4031fdef",
      parentSpanId: "6448bf429cf7dbaf",
      name: "chat gpt-4o",
      startTimeUnixNano: "1725038115961117000",
      endTimeUnixNano: "1725038117111910000",
      attributes: [
        { key: "gen_ai.operation.name", value: { stringValue: "chat" } },
        { key: "gen_ai.system", value: { stringValue: "openai" } },
        { key: "gen_ai.request.model", value: { stringValue: "gpt-4o" } },
        { key: "server.address", value: { stringValue: "api.openai.com" } },
        { key: "gen_ai.usage.input_tokens", value: { intValue: 90 } },
        { key: "gen_ai.usage.output_tokens", value: { intValue: 41 } },
        {
          key: "gen_ai.response.model",
          value: { stringValue: "gpt-4o-2024-08-06" },
        },
        {
          key: "events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.system": "openai", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"index": 0, "message": {"role": "assistant", "tool_calls": [{"id": "call_44WrLygWFlHJhslzz3b8ntRn", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}, {"id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}]}, "gen_ai.system": "openai", "event.name": "gen_ai.choice"}]',
          },
        },
        {
          key: "model_request_parameters",
          value: {
            stringValue:
              '{"function_tools": [{"name": "roll_dice", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Roll a six-sided die and return the result.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}, {"name": "get_player_name", "parameters_json_schema": {"additionalProperties": false, "properties": {}, "type": "object"}, "description": "Get the player\'s name.", "outer_typed_dict_key": null, "strict": false, "kind": "function"}], "builtin_tools": [], "output_mode": "text", "output_object": null, "output_tools": [], "allow_text_output": true, "temperature": 0.7, "max_tokens": 1000, "top_p": 0.9}',
          },
        },
        { key: "gen_ai.request.temperature", value: { doubleValue: 0.7 } },
        { key: "gen_ai.request.max_tokens", value: { intValue: 1000 } },
        { key: "gen_ai.request.top_p", value: { doubleValue: 0.9 } },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [], // Empty - events are in the attributes.events field as JSON
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiChatSpan);

    // This should be processed by pydantic-AI parser for events and GenAI for metadata
    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
      ],
      output: [
        {
          role: "assistant",
          content: null,
          tool_calls: [
            {
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
            {
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
          ],
        },
      ],
      metadata: expect.objectContaining({
        "gen_ai.operation.name": "chat",
        "gen_ai.system": "openai",
        "gen_ai.request.model": "gpt-4o",
        "gen_ai.request.temperature": 0.7,
        "gen_ai.request.max_tokens": 1000,
        "gen_ai.request.top_p": 0.9,
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 0.9,
        tools: [
          {
            type: "function",
            function: {
              name: "roll_dice",
              description: "Roll a six-sided die and return the result.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Get the player's name.",
              parameters: {
                additionalProperties: false,
                properties: {},
                type: "object",
              },
            },
          },
        ],
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 90,
        completion_tokens: 41,
        tokens: 131,
      }),
      span_attributes: {
        name: "chat gpt-4o",
        type: "llm",
      },
    });

    // Verify that events field was deleted after processing
    expect(row.metadata).not.toHaveProperty("events");

    // Both parsers should be active
    expect(fieldStats.toObject()).toEqual({
      pydanticAI: {
        errs: 0,
        ok: 3, // input, output, metadata (tools) - span_attributes only for chat spans now handled by GenAI
      },
      genAI: {
        errs: 0,
        ok: 3, // metadata, metrics, span_attributes
      },
      traceloop: {
        errs: 2,
        ok: 0,
      },
    });
  });

  it("should process pydantic-ai agent run with tool calls", () => {
    // Test the main agent run span - this contains the full conversation flow
    const pydanticAiAgentSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "6448bf429cf7dbaf",
      parentSpanId: null,
      name: "agent run",
      startTimeUnixNano: "1725038115960585000",
      endTimeUnixNano: "1725038118214845000",
      attributes: [
        { key: "model_name", value: { stringValue: "gpt-4o" } },
        { key: "agent_name", value: { stringValue: "agent" } },
        { key: "logfire.msg", value: { stringValue: "agent run" } },
        {
          key: "final_result",
          value: {
            stringValue:
              "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
          },
        },
        { key: "gen_ai.usage.input_tokens", value: { intValue: 237 } },
        { key: "gen_ai.usage.output_tokens", value: { intValue: 63 } },
        {
          key: "all_messages_events",
          value: {
            stringValue:
              '[{"role": "system", "content": "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player\'s name in the response.", "gen_ai.message.index": 0, "event.name": "gen_ai.system.message"}, {"content": "My guess is 4", "role": "user", "gen_ai.message.index": 0, "event.name": "gen_ai.user.message"}, {"role": "assistant", "tool_calls": [{"id": "call_44WrLygWFlHJhslzz3b8ntRn", "type": "function", "function": {"name": "get_player_name", "arguments": "{}"}}, {"id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "type": "function", "function": {"name": "roll_dice", "arguments": "{}"}}], "gen_ai.message.index": 1, "event.name": "gen_ai.assistant.message"}, {"content": "Anne", "role": "tool", "id": "call_44WrLygWFlHJhslzz3b8ntRn", "name": "get_player_name", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"content": "5", "role": "tool", "id": "call_1zDy4xWoCKHsT01U5Rt1wbpR", "name": "roll_dice", "gen_ai.message.index": 2, "event.name": "gen_ai.tool.message"}, {"role": "assistant", "content": "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!", "gen_ai.message.index": 3, "event.name": "gen_ai.assistant.message"}]',
          },
        },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
          },
        },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiAgentSpan);

    expect(row).toMatchObject({
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          role: "user",
          content: "My guess is 4",
        },
      ],
      output: [
        {
          role: "assistant",
          content: null,
          tool_calls: [
            {
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
              function: {
                name: "get_player_name",
                arguments: "{}",
              },
            },
            {
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
              function: {
                name: "roll_dice",
                arguments: "{}",
              },
            },
          ],
        },
        {
          role: "tool",
          content: "Anne",
          tool_call_id: "call_44WrLygWFlHJhslzz3b8ntRn",
        },
        {
          role: "tool",
          content: "5",
          tool_call_id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
        },
        {
          role: "assistant",
          content:
            "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        },
      ],
      metadata: expect.objectContaining({
        model_name: "gpt-4o",
        agent_name: "agent",
        final_result:
          "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 63,
      }),
      metrics: expect.objectContaining({
        prompt_tokens: 237,
        completion_tokens: 63,
        tokens: 300,
      }),
      span_attributes: expect.objectContaining({
        name: "agent run",
        type: "llm",
      }),
    });

    // Complete row validation
    expect(row).toEqual({
      _is_merge: false,
      created: "2024-08-30T17:15:15.960Z",
      error: null,
      id: "6448bf429cf7dbaf",
      input: [
        {
          role: "system",
          content:
            "You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.",
        },
        {
          content: "My guess is 4",
          role: "user",
        },
      ],
      metadata: {
        agent_name: "agent",
        final_result:
          "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 63,
        "logfire.json_schema":
          '{"type": "object", "properties": {"all_messages_events": {"type": "array"}, "final_result": {"type": "object"}}}',
        "logfire.msg": "agent run",
        model_name: "gpt-4o",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        "telemetry.sdk.name": "opentelemetry",
        "telemetry.sdk.version": "1.36.0",
      },
      metrics: {
        completion_tokens: 63,
        end: 1725038118.214845,
        prompt_tokens: 237,
        start: 1725038115.9605849,
        tokens: 300,
      },
      output: [
        {
          content: null,
          role: "assistant",
          tool_calls: [
            {
              function: {
                arguments: "{}",
                name: "get_player_name",
              },
              id: "call_44WrLygWFlHJhslzz3b8ntRn",
              type: "function",
            },
            {
              function: {
                arguments: "{}",
                name: "roll_dice",
              },
              id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
              type: "function",
            },
          ],
        },
        {
          role: "tool",
          content: "Anne",
          tool_call_id: "call_44WrLygWFlHJhslzz3b8ntRn",
        },
        {
          role: "tool",
          content: "5",
          tool_call_id: "call_1zDy4xWoCKHsT01U5Rt1wbpR",
        },
        {
          content:
            "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
          role: "assistant",
        },
      ],
      root_span_id: "45d84ea0ce3397672dfc3c695ef557fb",
      span_attributes: {
        name: "agent run",
        type: "llm",
      },
      span_id: "6448bf429cf7dbaf",
      span_parents: [],
    });

    // Verify that all_messages_events field was deleted after processing
    expect(row.metadata).not.toHaveProperty("all_messages_events");

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 3,
      },
      traceloop: {
        errs: 2,
        ok: 0,
      },
    });
  });

  it("should process pydantic-ai tool execution span", () => {
    // Test individual tool execution span
    const pydanticAiToolSpan = {
      traceId: "45d84ea0ce3397672dfc3c695ef557fb",
      spanId: "47b675db3f574886",
      parentSpanId: "4ed197363463db15",
      name: "running tool",
      startTimeUnixNano: "1725038117112825000",
      endTimeUnixNano: "1725038117113689000",
      attributes: [
        { key: "gen_ai.tool.name", value: { stringValue: "get_player_name" } },
        {
          key: "gen_ai.tool.call.id",
          value: { stringValue: "call_44WrLygWFlHJhslzz3b8ntRn" },
        },
        { key: "tool_arguments", value: { stringValue: "{}" } },
        {
          key: "logfire.msg",
          value: { stringValue: "running tool: get_player_name" },
        },
        { key: "tool_response", value: { stringValue: "Anne" } },
        {
          key: "logfire.json_schema",
          value: {
            stringValue:
              '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
          },
        },
        { key: "telemetry.sdk.language", value: { stringValue: "python" } },
        { key: "telemetry.sdk.name", value: { stringValue: "opentelemetry" } },
        { key: "telemetry.sdk.version", value: { stringValue: "1.36.0" } },
        { key: "service.name", value: { stringValue: "unknown_service" } },
      ],
      events: [],
      status: { code: 1 },
    };

    const { row, fieldStats } = otelSpanToRow(pydanticAiToolSpan);

    expect(row).toMatchObject({
      input: undefined, // Tool spans don't have conversational input
      output: undefined, // Tool spans don't have conversational output
      metadata: expect.objectContaining({
        "gen_ai.tool.name": "get_player_name",
        "gen_ai.tool.call.id": "call_44WrLygWFlHJhslzz3b8ntRn",
        tool_arguments: "{}",
        tool_response: "Anne",
        "logfire.msg": "running tool: get_player_name",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        tools: [
          {
            type: "function",
            function: {
              name: "get_player_name",
              description: "Tool: get_player_name",
              parameters: {
                type: "object",
                properties: {},
                required: [],
              },
            },
          },
        ],
      }),
      span_attributes: expect.objectContaining({
        name: "running tool",
        type: "tool",
      }),
    });

    // Complete row validation for tool execution
    expect(row).toEqual({
      _is_merge: false,
      created: "2024-08-30T17:15:17.112Z",
      error: null,
      id: "47b675db3f574886",
      input: undefined,
      metadata: {
        "gen_ai.tool.call.id": "call_44WrLygWFlHJhslzz3b8ntRn",
        "gen_ai.tool.name": "get_player_name",
        "logfire.json_schema":
          '{"type": "object", "properties": {"tool_arguments": {"type": "object"}, "tool_response": {"type": "object"}, "gen_ai.tool.name": {}, "gen_ai.tool.call.id": {}}}',
        "logfire.msg": "running tool: get_player_name",
        "service.name": "unknown_service",
        "telemetry.sdk.language": "python",
        "telemetry.sdk.name": "opentelemetry",
        "telemetry.sdk.version": "1.36.0",
        tool_arguments: "{}",
        tool_response: "Anne",
        tools: [
          {
            function: {
              description: "Tool: get_player_name",
              name: "get_player_name",
              parameters: {
                properties: {},
                required: [],
                type: "object",
              },
            },
            type: "function",
          },
        ],
      },
      metrics: {
        end: 1725038117.1136892,
        start: 1725038117.1128252,
      },
      output: undefined,
      root_span_id: "45d84ea0ce3397672dfc3c695ef557fb",
      span_attributes: {
        name: "running tool",
        type: "tool",
      },
      span_id: "47b675db3f574886",
      span_parents: ["4ed197363463db15"],
    });

    expect(fieldStats.toObject()).toEqual({
      genAI: {
        errs: 0,
        ok: 1,
      },
      pydanticAI: {
        errs: 0,
        ok: 1,
      },
      traceloop: {
        errs: 2,
        ok: 0,
      },
    });
  });
});
