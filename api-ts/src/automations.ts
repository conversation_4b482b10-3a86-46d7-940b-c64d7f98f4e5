import { parseQuery } from "@braintrust/btql/parser";
import { parseAndInterpretExpr } from "./btql_wasm";
import {
  ProjectAutomation,
  projectAutomationSchema,
  LogAutomation,
  logAutomationSchema,
  projectScoreSchema,
} from "@braintrust/core/typespecs";
import { getRedis } from "./redis";
import { PENDING_FLUSHABLES } from "./pending_flushables";
import {
  ObjectIdsUnion,
  OBJECT_TYPE_FIELD,
  CloudIdentity,
} from "@braintrust/local/api-schema";
import { _urljoin, mapAt, mapSetDefault, recordFind } from "@braintrust/core";
import { formatDuration, intervalToDuration } from "date-fns";
import {
  HTTPError,
  REDIS_AUTOMATION_CACHE_KEY,
  REDIS_AUTOMATION_CACHE_KEYS_SET_KEY,
  postDefaultHeaders,
  wrapZodError,
  AccessDeniedError,
  InternalServerError,
} from "./util";
import { type RequestContext } from "./request_context";
import { sha1 } from "./hash";
import { customFetchRequest } from "./custom_fetch";
import { z } from "zod";
import { OBJECT_CACHE } from "./object_cache";
import { runBtql } from "./btql";
import { getStorageToLogicalMap } from "./schema";
import { getLogger } from "./instrumentation/logger";
import { GetCallerIdentityCommand, STSClient } from "@aws-sdk/client-sts";
import { checkTokenAuthorized } from "./token_auth";
import { testBtqlExport } from "./cron/export";
import { testOnlineScoring } from "./async_scoring";

class AutomationCache {
  constructor(public expirationTime = 3600) {}

  public async getAutomationMulti({
    appOrigin,
    authToken,
    projectIds,
    wasCachedToken,
  }: {
    projectIds: string[];
    appOrigin: string;
    authToken: string | undefined;
    wasCachedToken?: string;
  }): Promise<Map<string, ProjectAutomation[]>> {
    const projectIdToCacheKeys = new Map<string, string>();
    projectIds.forEach((project_id) => {
      if (!projectIdToCacheKeys.has(project_id)) {
        const cacheKey = AutomationCache.makeCacheKey({
          projectId: project_id,
          token: authToken || "anon",
        });
        projectIdToCacheKeys.set(project_id, cacheKey);
      }
    });

    const projectIdToConfig = new Map<string, ProjectAutomation[]>();
    if (projectIdToCacheKeys.size === 0) {
      return projectIdToConfig;
    }

    const allProjectIds = [...projectIdToCacheKeys.keys()];
    const redisClient = await getRedis();
    const allConfigValues = await Promise.all(
      allProjectIds.map((projectId) =>
        redisClient.get(mapAt(projectIdToCacheKeys, projectId)),
      ),
    );

    const notFoundProjectIds: string[] = [];
    allProjectIds.forEach((projectId, i) => {
      const configValue = allConfigValues[i];
      if (configValue) {
        const automations = projectAutomationSchema
          .array()
          .parse(JSON.parse(configValue));
        projectIdToConfig.set(projectId, automations);
      } else {
        notFoundProjectIds.push(projectId);
      }
    });

    if (notFoundProjectIds.length === 0) {
      if (wasCachedToken) {
        await redisClient.set(wasCachedToken, "true", { EX: 3600 });
      }
      return projectIdToConfig;
    }

    // We need to query the app DB for the missing automations.
    const resp = await customFetchRequest(
      _urljoin(appOrigin, "/api/project_automation/get"),
      {
        method: "POST",
        headers: postDefaultHeaders({ token: authToken }),
        body: JSON.stringify({
          project_id: notFoundProjectIds,
        }),
      },
    );
    if (!resp.ok) {
      throw new HTTPError(
        resp.statusCode,
        `Failed to get project automations:\n${await resp.body.text()}`,
      );
    }
    const responseConfigs = projectAutomationSchema
      .array()
      .parse(await resp.body.json());
    // Make sure to initialize the missing project IDs with an empty array, so
    // that we cache results for projects with automations.
    const projectIdToFetchedConfigs = new Map<string, ProjectAutomation[]>(
      notFoundProjectIds.map((projectId): [string, ProjectAutomation[]] => [
        projectId,
        [],
      ]),
    );
    responseConfigs.forEach((automation) => {
      mapAt(projectIdToFetchedConfigs, automation.project_id).push(automation);
    });

    // Save the configs in redis.
    await Promise.all(
      [...projectIdToFetchedConfigs.entries()].flatMap(
        ([projectId, automations]): Promise<unknown>[] => {
          const cacheKey = mapAt(projectIdToCacheKeys, projectId);
          const setKey = AutomationCache.makeSetKey({ projectId });
          const value = JSON.stringify(automations);
          return [
            redisClient.set(cacheKey, value, { EX: this.expirationTime }),
            redisClient.sAdd(setKey, cacheKey),
            redisClient.expire(setKey, this.expirationTime),
          ];
        },
      ),
    );

    // Merge the fetched configs with the cached ones and return.
    projectIdToFetchedConfigs.forEach((configs, projectId) => {
      projectIdToConfig.set(projectId, configs);
    });
    return projectIdToConfig;
  }

  public async getAutomationsById({
    appOrigin,
    authToken,
    automationIds,
    wasCachedToken,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    automationIds: string[];
    wasCachedToken?: string;
  }): Promise<Map<string, ProjectAutomation>> {
    const projectIdToCacheKeys = new Map<string, string>();
    automationIds.forEach((automation_id) => {
      if (!projectIdToCacheKeys.has(automation_id)) {
        const cacheKey = AutomationCache.makeCacheKey({
          automationId: automation_id,
          token: authToken || "anon",
        });
        projectIdToCacheKeys.set(automation_id, cacheKey);
      }
    });

    const automationIdToConfig = new Map<string, ProjectAutomation>();
    if (projectIdToCacheKeys.size === 0) {
      return automationIdToConfig;
    }

    const allAutomationIds = [...projectIdToCacheKeys.keys()];
    const redisClient = await getRedis();
    const allConfigValues = await Promise.all(
      allAutomationIds.map((automationId) =>
        redisClient.get(mapAt(projectIdToCacheKeys, automationId)),
      ),
    );

    const notFoundAutomationIds: string[] = [];
    allAutomationIds.forEach((automationId, i) => {
      const configValue = allConfigValues[i];
      if (configValue) {
        const automation = projectAutomationSchema.parse(
          JSON.parse(configValue),
        );
        automationIdToConfig.set(automationId, automation);
      } else {
        notFoundAutomationIds.push(automationId);
      }
    });

    if (notFoundAutomationIds.length === 0) {
      if (wasCachedToken) {
        await redisClient.set(wasCachedToken, "true", { EX: 3600 });
      }
      return automationIdToConfig;
    }

    // We need to query the app DB for the missing automations.
    const resp = await customFetchRequest(
      _urljoin(appOrigin, "/api/project_automation/get"),
      {
        method: "POST",
        headers: postDefaultHeaders({ token: authToken }),
        body: JSON.stringify({
          id: notFoundAutomationIds,
        }),
      },
    );
    if (!resp.ok) {
      throw new HTTPError(
        resp.statusCode,
        `Failed to get project automations:\n${await resp.body.text()}`,
      );
    }
    const responseConfigs = projectAutomationSchema
      .array()
      .parse(await resp.body.json());
    const automationIdToFetchedConfig = new Map<string, ProjectAutomation>();
    responseConfigs.forEach((automation) => {
      automationIdToFetchedConfig.set(automation.id, automation);
    });

    // Save the configs in redis.
    await Promise.all(
      [...automationIdToFetchedConfig.entries()].flatMap(
        ([automationId, automation]): Promise<unknown>[] => {
          const cacheKey = mapAt(projectIdToCacheKeys, automationId);
          const setKey = AutomationCache.makeSetKey({ automationId });
          const value = JSON.stringify(automation);
          return [
            redisClient.set(cacheKey, value, { EX: this.expirationTime }),
            redisClient.sAdd(setKey, cacheKey),
            redisClient.expire(setKey, this.expirationTime),
          ];
        },
      ),
    );

    // Merge the fetched configs with the cached ones and return.
    automationIdToFetchedConfig.forEach((automation, automationId) => {
      automationIdToConfig.set(automationId, automation);
    });
    return automationIdToConfig;
  }

  public async getAutomationById({
    appOrigin,
    authToken,
    automationId,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    automationId: string;
  }): Promise<ProjectAutomation | undefined> {
    const automations = await AUTOMATION_CACHE.getAutomationsById({
      appOrigin: appOrigin,
      authToken,
      automationIds: [automationId],
    });
    return automations.get(automationId);
  }

  public async flushEntries({
    projectId,
    automationId,
  }: {
    projectId?: string;
    automationId?: string;
  }) {
    const setKey = AutomationCache.makeSetKey({ projectId, automationId });
    const redisClient = await getRedis();
    const matchingKeys = await redisClient.sMembers(setKey);
    if (matchingKeys.length > 0) {
      await Promise.all([
        redisClient.del(matchingKeys),
        redisClient.sRem(setKey, matchingKeys),
      ]);
    }
  }

  public async flushAutomationInterval({
    automationId,
  }: {
    automationId: string;
  }) {
    const key = AutomationCache.makeLastRunKey({ automationId });
    const redisClient = await getRedis();
    await redisClient.del(key);
  }

  private static makeCacheKey({
    projectId,
    automationId,
    token,
  }: {
    projectId?: string;
    automationId?: string;
    token: string;
  }): string {
    if (!automationId && !projectId) {
      throw new Error("Either projectId or automationId must be provided");
    }
    if (automationId && projectId) {
      throw new Error("Only one of projectId or automationId can be provided");
    }
    if (projectId) {
      return [
        REDIS_AUTOMATION_CACHE_KEY,
        "project",
        projectId,
        sha1(token),
      ].join(":");
    } else {
      return [
        REDIS_AUTOMATION_CACHE_KEY,
        "automation",
        automationId,
        sha1(token),
      ].join(":");
    }
  }

  private static makeSetKey({
    projectId,
    automationId,
  }: {
    projectId?: string;
    automationId?: string;
  }): string {
    if (!automationId && !projectId) {
      throw new Error("Either projectId or automationId must be provided");
    }
    if (automationId && projectId) {
      throw new Error("Only one of projectId or automationId can be provided");
    }
    if (projectId) {
      return [REDIS_AUTOMATION_CACHE_KEYS_SET_KEY, "project", projectId].join(
        ":",
      );
    } else {
      return [
        REDIS_AUTOMATION_CACHE_KEYS_SET_KEY,
        "automation",
        automationId,
      ].join(":");
    }
  }

  public static makeLastRunKey({
    automationId,
  }: {
    automationId: string;
  }): string {
    return `automation:last_run:${automationId}`;
  }
}

export const AUTOMATION_CACHE = new AutomationCache();

export async function flushAutomationCache({
  appOrigin,
  authToken,
  ctxData,
}: {
  appOrigin: string;
  authToken: string | undefined;
  ctxData: unknown;
}) {
  const params = wrapZodError(() =>
    z
      .object({
        project_id: z.string(),
      })
      .parse(ctxData),
  );

  // Make sure the requester has read access to the project.
  const objectCacheEntry = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken,
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: params.project_id,
  });
  if (!objectCacheEntry.permissions.includes("read")) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType: "project",
      objectId: params.project_id,
    });
  }

  // Get all the automation ids for the project
  const automations = await AUTOMATION_CACHE.getAutomationMulti({
    appOrigin,
    authToken,
    projectIds: [params.project_id],
  });
  const automationIds = Array.from(automations.values()).flatMap(
    (automations) => automations.map((automation) => automation.id),
  );

  await Promise.all([
    AUTOMATION_CACHE.flushEntries({
      projectId: params.project_id,
    }),
    ...automationIds.map((automationId) =>
      AUTOMATION_CACHE.flushAutomationInterval({ automationId }),
    ),
  ]);
}

async function setAutomationLastRun(automation: LogAutomation): Promise<void> {
  const redisClient = await getRedis();
  await redisClient.set(
    AutomationCache.makeLastRunKey({ automationId: automation.id }),
    new Date().toISOString(),
    {
      EX: automation.config.interval_seconds,
    },
  );
}

async function shouldRunAutomation(
  automation: LogAutomation,
): Promise<boolean> {
  const redisClient = await getRedis();
  const value = await redisClient.get(
    AutomationCache.makeLastRunKey({ automationId: automation.id }),
  );
  if (!value) {
    return true;
  }
  const lastRunDate = new Date(value);
  const now = new Date();
  return (
    now.getTime() - lastRunDate.getTime() >
    automation.config.interval_seconds * 1000
  );
}

async function getMatchingRows(
  automation: LogAutomation,
  rows: Record<string, unknown>[],
) {
  if (automation.config.btql_filter === "") {
    return rows.length;
  }

  return rows
    .map((row) => {
      // Normalize the row before running the BTQL filter.
      // NOTE: when support for other event types is added, we will need to
      // normalize the row based on the object type instead of hardcoding project_logs
      // or skip normalization entirely if the rows are coming directly from a BTQL query.
      const columnStorageToLogical = getStorageToLogicalMap("project_logs");
      return Object.fromEntries(
        Object.entries(row).map(([k, v]) => [
          recordFind(columnStorageToLogical, k) ?? k,
          v,
        ]),
      );
    })
    .reduce((acc: number, row: Record<string, unknown>) => {
      try {
        const result = parseAndInterpretExpr({
          expr: automation.config.btql_filter,
          row,
        });
        if (result === true) {
          return acc + 1;
        }
      } catch (e) {
        getLogger().error(
          {
            error: e,
          },
          "Error running BTQL filter",
        );
      }

      return acc;
    }, 0);
}

// testing an unsaved automation means we may not have an id
const testAutomationParamsSchema = z.union([
  projectAutomationSchema
    .pick({
      project_id: true,
      name: true,
      description: true,
      config: true,
    })
    .extend({
      id: z.string().optional(),
    }),
  projectScoreSchema
    .pick({
      project_id: true,
      name: true,
      description: true,
      score_type: true,
    })
    .extend({
      id: z.string().optional(),
      config: projectScoreSchema.shape.config.unwrap().unwrap(),
    }),
]);

const _executeAutomationParamsSchema = logAutomationSchema
  .pick({
    project_id: true,
    name: true,
    description: true,
    config: true,
  })
  .extend({
    id: z.string().optional(),
  });

type ExecuteAutomationArgs = {
  appOrigin: string;
  authToken: string | undefined;
  rowCount: number;
  windowEnd: Date;
  automation: z.infer<typeof _executeAutomationParamsSchema>;
  isTest: boolean;
};

type AutomationActionContext = {
  organization: {
    id: string;
    name: string;
  };
  project: {
    id: string;
    name: string;
  };
  automation: {
    id: string | null;
    name: string;
    description: string | null;
    event_type: string;
    btql_filter: string | null;
    interval_seconds: number;
    url: string;
  };
  details: {
    is_test: boolean;
    message: string;
    time_start: string;
    time_end: string;
    count: number;
    related_logs_url: string;
  };
};

type ExecuteAutomationResult =
  | { kind: "success"; payload: AutomationActionContext }
  | { kind: "error"; message: string };

async function executeLogsAutomationAction({
  appOrigin,
  authToken,
  automation,
  isTest,
  rowCount,
  windowEnd,
}: ExecuteAutomationArgs): Promise<ExecuteAutomationResult> {
  if (automation.config.event_type !== "logs") {
    throw new InternalServerError(
      `Automation is not a logs automation: ${automation.config.event_type}`,
    );
  }

  const project = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken,
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: automation.project_id,
  });
  const organization = project.parent_cols.get("organization");
  if (!organization) {
    throw new InternalServerError(
      `Project ${automation.project_id} has no organization information`,
    );
  }
  const orgName = organization.name;

  const windowStart = new Date(
    windowEnd.getTime() - automation.config.interval_seconds * 1000,
  );

  const logsFilter = {
    filter: [
      ...(automation.config.btql_filter === ""
        ? []
        : [
            {
              text: automation.config.btql_filter,
              label: automation.config.btql_filter,
              originType: "btql",
            },
          ]),
      {
        text: `created >= "${windowStart.toISOString()}" AND created < "${windowEnd.toISOString()}"`,
        label: `created >= "${windowStart.toISOString()}" AND created < "${windowEnd.toISOString()}"`,
        originType: "btql",
      },
    ],
  };
  const logsUrl = `${appOrigin}/app/${orgName}/p/${project.object_name}/logs?search=${encodeURIComponent(JSON.stringify(logsFilter))}`;
  const automationUrl = `${appOrigin}/app/${orgName}/p/${project.object_name}/configuration/alerts?aid=${isTest && !automation.id ? "new" : automation.id}`;
  const duration = intervalToDuration({
    start: 0,
    end: automation.config.interval_seconds * 1000,
  });
  const humanizedInterval = formatDuration(duration, {
    format: ["seconds", "minutes", "hours", "days", "months"],
  });

  // may need to update the zapier integration (new_event.ts) sample if this shape changes
  const actionContext: AutomationActionContext = {
    organization: {
      id: organization.id,
      name: organization.name,
    },
    project: {
      id: automation.project_id,
      name: project.object_name,
    },
    automation: {
      id: automation.id ?? null,
      name: automation.name,
      description: automation.description ?? null,
      event_type: automation.config.event_type,
      btql_filter: automation.config.btql_filter || null,
      interval_seconds: automation.config.interval_seconds,
      url: automationUrl,
    },
    details: {
      is_test: isTest,
      message: `${automation.name}: ${rowCount} logs triggered alert in the past ${humanizedInterval}`,
      time_start: windowStart.toISOString(),
      time_end: windowEnd.toISOString(),
      count: rowCount,
      related_logs_url: logsUrl,
    },
  };

  const attempts = isTest ? 1 : 3;
  for (let i = 0; i < attempts; i++) {
    const lastAttempt = i === attempts - 1;
    let errorMessage = "";

    switch (automation.config.action.type) {
      case "webhook":
        try {
          const resp = await customFetchRequest(automation.config.action.url, {
            method: "POST",
            body: JSON.stringify(actionContext),
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (resp.ok) {
            return { kind: "success", payload: actionContext };
          }

          errorMessage = `Webhook returned error status ${resp.statusCode}${lastAttempt ? "" : ", retrying"}: ${await resp.body.text()}`;
        } catch (e) {
          errorMessage = `Error calling webhook${lastAttempt ? "" : ", retrying"}: ${e}`;
        }
        break;
      default:
        const _exhaustiveCheck: never = automation.config.action.type;
        throw new Error(`Unknown action type: ${_exhaustiveCheck}`);
    }

    getLogger().error(
      {
        errorMessage,
      },
      "Error executing automation action",
    );
    if (lastAttempt) {
      return { kind: "error", message: errorMessage };
    }

    await new Promise((resolve) => setTimeout(resolve, (i + 1) * 1000));
  }

  // unreachable, appease typescript
  return {
    kind: "error",
    message: `Failed to execute automation action after ${attempts} attempts`,
  };
}

export type TestAutomationResponse =
  | { kind: "success"; payload: unknown }
  | { kind: "error"; message: string };

export async function testAutomation(
  ctx: RequestContext,
): Promise<TestAutomationResponse> {
  await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
  });

  const automation = wrapZodError(() =>
    testAutomationParamsSchema.parse(ctx.data),
  );

  const projectInfo = await OBJECT_CACHE.checkAndGet({
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: automation.project_id,
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
  });
  if (!projectInfo) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "project",
      objectId: automation.project_id,
    });
  }
  const organization = projectInfo.parent_cols.get("organization");
  if (!organization) {
    throw new InternalServerError(
      `Project ${automation.project_id} has no organization information`,
    );
  }
  const orgId = organization.id;

  if ("score_type" in automation) {
    if (automation.score_type !== "online") {
      throw new InternalServerError(
        `Only online scoring is supported for testing, got ${automation.score_type}`,
      );
    }

    return await testOnlineScoring(
      {
        orgName: organization.name,
        projectId: automation.project_id,
        automation,
      },
      ctx,
    );
  }

  const eventType = automation.config.event_type;
  if (eventType === "logs") {
    let rowCount = 0;
    const now = new Date();
    const windowExpr = `created > "${now.toISOString()}" - INTERVAL ${automation.config.interval_seconds} SECOND`;
    const btqlFilter =
      automation.config.btql_filter === ""
        ? "true"
        : automation.config.btql_filter;
    try {
      const queryStr = `dimensions: ${windowExpr} AS window | measures: count(1) AS row_count | from: project_logs("${automation.project_id}") spans | filter: (${windowExpr}) AND (${btqlFilter})`;
      const query = parseQuery(queryStr);
      const btqlResult = await runBtql({
        body: {
          query,
          use_brainstore: true,
          brainstore_realtime: true,
        },
        appOrigin: ctx.appOrigin,
        ctxToken: ctx.token,
      });
      if (!("rows" in btqlResult) || btqlResult.rows.length > 1) {
        return {
          kind: "error",
          message: `Failed to run BTQL query: ${JSON.stringify(btqlResult)}`,
        };
      }
      if (
        btqlResult.rows.length > 0 &&
        typeof btqlResult.rows[0].row_count === "number"
      ) {
        rowCount = btqlResult.rows[0].row_count;
      }
    } catch (e) {
      return {
        kind: "error",
        message: `Failed to run BTQL query: ${e}`,
      };
    }

    if (rowCount === 0) {
      return {
        kind: "error",
        message:
          "No matching rows found for the provided BTQL filter and interval. Adjust the filter or interval or log rows to test again.",
      };
    }

    const result = await executeLogsAutomationAction({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      // appease typescript
      automation: {
        ...automation,
        config: {
          ...automation.config,
        },
      },
      isTest: true,
      rowCount: rowCount,
      windowEnd: now,
    });

    if (result.kind === "error") {
      return result;
    }
    return {
      kind: "success",
      payload: result.payload,
    };
  } else if (eventType === "btql_export") {
    return await testBtqlExport({
      orgId,
      automation,
    });
  } else if (eventType === "retention") {
    throw new InternalServerError("Cannot test retention automations");
  } else {
    const _exhaustiveCheck: never = eventType;
    throw new InternalServerError(
      `Unknown automation event type: ${_exhaustiveCheck}`,
    );
  }
}

async function executeLogAutomation({
  appOrigin,
  authToken,
  automation,
  rows,
}: {
  appOrigin: string;
  authToken: string | undefined;
  automation: LogAutomation;
  rows: Record<string, unknown>[];
}) {
  if (!(await shouldRunAutomation(automation))) {
    return;
  }

  const rowCount = await getMatchingRows(automation, rows);
  if (rowCount === 0) {
    return;
  }

  await setAutomationLastRun(automation);

  const now = new Date();
  const result = await executeLogsAutomationAction({
    appOrigin,
    authToken,
    automation,
    isTest: false,
    rowCount,
    windowEnd: now,
  });

  if (result.kind === "error") {
    await AUTOMATION_CACHE.flushAutomationInterval({
      automationId: automation.id,
    });

    getLogger().error(
      {
        errorMessage: result.message,
        automationId: automation.id,
      },
      "Error executing automation",
    );
  }
}

type InsertedRow = {
  fullRowData: Record<string, unknown>;
  objectIds: ObjectIdsUnion;
};

export function executeLogAutomations({
  appOrigin,
  authToken,
  projectIdToAutomations,
  insertedRows,
}: {
  appOrigin: string;
  authToken: string | undefined;
  projectIdToAutomations: Map<string, ProjectAutomation[]>;
  insertedRows: InsertedRow[];
}) {
  const projectIdToRows = insertedRows.reduce(
    (acc: Map<string, Record<string, unknown>[]>, row) => {
      // Filter to just project_logs to run log automations.
      if (row.objectIds[OBJECT_TYPE_FIELD] === "project_logs") {
        mapSetDefault(acc, row.objectIds.project_id, []).push(row.fullRowData);
      }
      return acc;
    },
    new Map<string, Record<string, unknown>[]>(),
  );
  projectIdToRows.forEach((rows, projectId) => {
    const automations = projectIdToAutomations.get(projectId) || [];
    automations.forEach((automation) => {
      if (automation.config.event_type === "logs") {
        PENDING_FLUSHABLES.add(
          executeLogAutomation({
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- typescript is not smart enough to narrow this type
            automation: automation as LogAutomation,
            rows,
            appOrigin,
            authToken,
          }),
        );
      }
    });
  });
}

let identity: CloudIdentity | undefined;
export async function getCloudIdentity(): Promise<CloudIdentity> {
  if (identity) {
    return identity;
  }

  identity = await refetchCloudIdentity();
  return identity;
}

async function refetchCloudIdentity() {
  const sts = new STSClient({});

  try {
    const command = new GetCallerIdentityCommand({});
    const response = await sts.send(command);

    if (!response.Account) {
      return {};
    }

    return { aws_account_id: response.Account };
  } catch (e) {
    getLogger().error(
      {
        error: e,
      },
      "Error getting AWS identity",
    );
    return {};
  }
}
