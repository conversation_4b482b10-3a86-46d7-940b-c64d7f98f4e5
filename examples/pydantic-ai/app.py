import os
import random

from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent

provider = TracerProvider()
trace.set_tracer_provider(provider)

provider.add_span_processor(BraintrustSpanProcessor())

# Add console exporter if environment variable is set
if os.getenv("BRAINTRUST_OTEL_CONSOLE_EXPORT", "").lower() in ("true", "1", "yes"):
    console_exporter = ConsoleSpanExporter()
    console_processor = BatchSpanProcessor(console_exporter)
    provider.add_span_processor(console_processor)

Agent.instrument_all()

agent = Agent(
    "openai:gpt-4o",
    system_prompt=(
        "You are a dice game host. Roll the dice for the player and check if their guess matches. "
        "Always include the player's name in the response."
    ),
)


@agent.tool_plain
def roll_dice() -> str:
    """Roll a six-sided die and return the result."""
    return str(random.randint(1, 6))


@agent.tool
def get_player_name(ctx: RunContext[str]) -> str:
    """Get the player's name."""
    return ctx.deps


dice_result = agent.run_sync("My guess is 4", deps="Anne")
print(dice_result)
