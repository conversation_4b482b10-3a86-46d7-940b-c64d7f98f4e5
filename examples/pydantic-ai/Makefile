.PHONY: help install run clean

help:
	@echo "Available commands:"
	@echo "  make install  - Install Python dependencies"
	@echo "  make run      - Run the pydantic-ai application"
	@echo "  make clean    - Clean up Python cache files"
	@echo ""
	@echo "Environment variables required:"
	@echo "  BRAINTRUST_API_KEY - Your Braintrust API key"
	@echo "  BRAINTRUST_API_URL - Your Braintrust API URL (optional)"
	@echo "  BRAINTRUST_PARENT - Your project name (optional)"

install:
	uv pip install -r requirements.txt

run:
	python app.py

clean:
	rm -rf __pycache__
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
