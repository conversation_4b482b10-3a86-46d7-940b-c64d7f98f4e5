{
    "name": "chat gpt-4o",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0x383402bd4031fdef",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": "0x6448bf429cf7dbaf",
    "start_time": "2025-08-20T18:55:15.961117Z",
    "end_time": "2025-08-20T18:55:17.111910Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "gen_ai.operation.name": "chat",
        "gen_ai.system": "openai",
        "gen_ai.request.model": "gpt-4o",
        "server.address": "api.openai.com",
        "model_request_parameters": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}",
        "gen_ai.usage.input_tokens": 90,
        "gen_ai.usage.output_tokens": 41,
        "gen_ai.response.model": "gpt-4o-2024-08-06",
        "events": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"index\": 0, \"message\": {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_44WrLygWFlHJhslzz3b8ntRn\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}, {\"id\": \"call_1zDy4xWoCKHsT01U5Rt1wbpR\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}]}, \"gen_ai.system\": \"openai\", \"event.name\": \"gen_ai.choice\"}]",
        "logfire.json_schema": "{\"type\": \"object\", \"properties\": {\"events\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
{
    "name": "running tool",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0x47b675db3f574886",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": "0x4ed197363463db15",
    "start_time": "2025-08-20T18:55:17.112825Z",
    "end_time": "2025-08-20T18:55:17.113689Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "gen_ai.tool.name": "get_player_name",
        "gen_ai.tool.call.id": "call_44WrLygWFlHJhslzz3b8ntRn",
        "tool_arguments": "{}",
        "logfire.msg": "running tool: get_player_name",
        "logfire.json_schema": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}",
        "tool_response": "Anne"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
{
    "name": "running tool",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0xa5fd44e57fd081d1",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": "0x4ed197363463db15",
    "start_time": "2025-08-20T18:55:17.113012Z",
    "end_time": "2025-08-20T18:55:17.113762Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "gen_ai.tool.name": "roll_dice",
        "gen_ai.tool.call.id": "call_1zDy4xWoCKHsT01U5Rt1wbpR",
        "tool_arguments": "{}",
        "logfire.msg": "running tool: roll_dice",
        "logfire.json_schema": "{\"type\": \"object\", \"properties\": {\"tool_arguments\": {\"type\": \"object\"}, \"tool_response\": {\"type\": \"object\"}, \"gen_ai.tool.name\": {}, \"gen_ai.tool.call.id\": {}}}",
        "tool_response": "5"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
{
    "name": "running tools",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0x4ed197363463db15",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": "0x6448bf429cf7dbaf",
    "start_time": "2025-08-20T18:55:17.112683Z",
    "end_time": "2025-08-20T18:55:17.113809Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "tools": [
            "get_player_name",
            "roll_dice"
        ],
        "logfire.msg": "running 2 tools"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
{
    "name": "chat gpt-4o",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0xb781986c2b966f0c",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": "0x6448bf429cf7dbaf",
    "start_time": "2025-08-20T18:55:17.114305Z",
    "end_time": "2025-08-20T18:55:18.214347Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "gen_ai.operation.name": "chat",
        "gen_ai.system": "openai",
        "gen_ai.request.model": "gpt-4o",
        "server.address": "api.openai.com",
        "model_request_parameters": "{\"function_tools\": [{\"name\": \"roll_dice\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Roll a six-sided die and return the result.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}, {\"name\": \"get_player_name\", \"parameters_json_schema\": {\"additionalProperties\": false, \"properties\": {}, \"type\": \"object\"}, \"description\": \"Get the player's name.\", \"outer_typed_dict_key\": null, \"strict\": false, \"kind\": \"function\"}], \"builtin_tools\": [], \"output_mode\": \"text\", \"output_object\": null, \"output_tools\": [], \"allow_text_output\": true}",
        "gen_ai.usage.input_tokens": 147,
        "gen_ai.usage.output_tokens": 22,
        "gen_ai.response.model": "gpt-4o-2024-08-06",
        "events": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_44WrLygWFlHJhslzz3b8ntRn\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}, {\"id\": \"call_1zDy4xWoCKHsT01U5Rt1wbpR\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}], \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 1, \"event.name\": \"gen_ai.assistant.message\"}, {\"content\": \"Anne\", \"role\": \"tool\", \"id\": \"call_44WrLygWFlHJhslzz3b8ntRn\", \"name\": \"get_player_name\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"content\": \"5\", \"role\": \"tool\", \"id\": \"call_1zDy4xWoCKHsT01U5Rt1wbpR\", \"name\": \"roll_dice\", \"gen_ai.system\": \"openai\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!\"}, \"gen_ai.system\": \"openai\", \"event.name\": \"gen_ai.choice\"}]",
        "logfire.json_schema": "{\"type\": \"object\", \"properties\": {\"events\": {\"type\": \"array\"}, \"model_request_parameters\": {\"type\": \"object\"}}}"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
{
    "name": "agent run",
    "context": {
        "trace_id": "0x45d84ea0ce3397672dfc3c695ef557fb",
        "span_id": "0x6448bf429cf7dbaf",
        "trace_state": "[]"
    },
    "kind": "SpanKind.INTERNAL",
    "parent_id": null,
    "start_time": "2025-08-20T18:55:15.960585Z",
    "end_time": "2025-08-20T18:55:18.214845Z",
    "status": {
        "status_code": "UNSET"
    },
    "attributes": {
        "model_name": "gpt-4o",
        "agent_name": "agent",
        "logfire.msg": "agent run",
        "final_result": "Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!",
        "gen_ai.usage.input_tokens": 237,
        "gen_ai.usage.output_tokens": 63,
        "all_messages_events": "[{\"role\": \"system\", \"content\": \"You are a dice game host. Roll the dice for the player and check if their guess matches. Always include the player's name in the response.\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.system.message\"}, {\"content\": \"My guess is 4\", \"role\": \"user\", \"gen_ai.message.index\": 0, \"event.name\": \"gen_ai.user.message\"}, {\"role\": \"assistant\", \"tool_calls\": [{\"id\": \"call_44WrLygWFlHJhslzz3b8ntRn\", \"type\": \"function\", \"function\": {\"name\": \"get_player_name\", \"arguments\": \"{}\"}}, {\"id\": \"call_1zDy4xWoCKHsT01U5Rt1wbpR\", \"type\": \"function\", \"function\": {\"name\": \"roll_dice\", \"arguments\": \"{}\"}}], \"gen_ai.message.index\": 1, \"event.name\": \"gen_ai.assistant.message\"}, {\"content\": \"Anne\", \"role\": \"tool\", \"id\": \"call_44WrLygWFlHJhslzz3b8ntRn\", \"name\": \"get_player_name\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"content\": \"5\", \"role\": \"tool\", \"id\": \"call_1zDy4xWoCKHsT01U5Rt1wbpR\", \"name\": \"roll_dice\", \"gen_ai.message.index\": 2, \"event.name\": \"gen_ai.tool.message\"}, {\"role\": \"assistant\", \"content\": \"Hello Anne! You guessed 4, but the dice rolled a 5. Better luck next time!\", \"gen_ai.message.index\": 3, \"event.name\": \"gen_ai.assistant.message\"}]",
        "logfire.json_schema": "{\"type\": \"object\", \"properties\": {\"all_messages_events\": {\"type\": \"array\"}, \"final_result\": {\"type\": \"object\"}}}"
    },
    "events": [],
    "links": [],
    "resource": {
        "attributes": {
            "telemetry.sdk.language": "python",
            "telemetry.sdk.name": "opentelemetry",
            "telemetry.sdk.version": "1.36.0",
            "service.name": "unknown_service"
        },
        "schema_url": ""
    }
}
