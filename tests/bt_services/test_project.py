from braintrust_local.constants import TESTING_ONLY_KV_INJECT_TIMEOUT_SLEEP_HEADER, WAS_UDF_CACHED_HEADER

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase

PROJECT_NAME = "foobar2"


class ProjectTest(BraintrustAppTestBase):
    def test_project_register_cache(self):
        # Register a project
        register_payload = {"org_name": "braintrustdata.com", "project_name": PROJECT_NAME}

        # First registration
        resp = self.run_request("post", f"{LOCAL_APP_URL}/api/project/register", json=register_payload)
        self.assertTrue(resp.ok, resp.text)
        self.assertNotIn(WAS_UDF_CACHED_HEADER, resp.headers)
        first_project = resp.json()["project"]
        first_project_id = first_project["id"]

        # Verify the project was created with expected properties
        self.assertEqual(first_project["name"], PROJECT_NAME)
        self.assertEqual(first_project["org_id"], self.org_id)
        self.assertIsNotNone(first_project_id)

        # Register the project again, and the header should be there, with the
        # exact same information.
        resp = self.run_request("post", f"{LOCAL_APP_URL}/api/project/register", json=register_payload)
        self.assertTrue(resp.ok, resp.text)
        self.assertIn(WAS_UDF_CACHED_HEADER, resp.headers)
        self.assertEqual(first_project, resp.json()["project"])

        # If we register with an injected timeout, we should get the same
        # response, but not cached.
        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/project/register",
            json=register_payload,
            headers={
                "Authorization": f"Bearer {self.org_api_key}",
                TESTING_ONLY_KV_INJECT_TIMEOUT_SLEEP_HEADER: "true",
            },
        )
        self.assertTrue(resp.ok, resp.text)
        self.assertNotIn(WAS_UDF_CACHED_HEADER, resp.headers)
        self.assertEqual(first_project, resp.json()["project"])

        # Delete the project
        delete_resp = self.run_request("post", f"{LOCAL_APP_URL}/api/project/delete_id", json={"id": first_project_id})
        self.assertTrue(delete_resp.ok, delete_resp.text)

        # Register the same project again
        resp2 = self.run_request("post", f"{LOCAL_APP_URL}/api/project/register", json=register_payload)
        self.assertTrue(resp2.ok, resp2.text)
        self.assertNotIn(WAS_UDF_CACHED_HEADER, resp2.headers)
        second_project = resp2.json()["project"]
        second_project_id = second_project["id"]

        # Verify the second project has the same name but different ID
        self.assertEqual(second_project["name"], PROJECT_NAME)
        self.assertEqual(second_project["org_id"], self.org_id)
        self.assertIsNotNone(second_project_id)
        self.assertNotEqual(first_project_id, second_project_id)
