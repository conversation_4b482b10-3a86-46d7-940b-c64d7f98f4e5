repos:
  - repo: "https://github.com/pre-commit/pre-commit-hooks"
    rev: v4.4.0
    hooks:
      - id: end-of-file-fixer
        exclude: (api-docs/templates/.*)|(api-ts/src/proxy/test-proxy/snapshots/.*)|(openapi/openapi/spec.yaml)
      - id: trailing-whitespace
        exclude: (app/pages/docs/cookbook/.*)|(api-docs/templates/.*)|(api-ts/src/proxy/test-proxy/snapshots/.*)|(openapi/openapi/spec.yaml)
  - repo: "https://github.com/psf/black"
    rev: 25.1.0
    hooks:
      - id: black
        files: ./
        args: [--config=./pyproject.toml]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.12.7
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.5
    hooks:
      - id: codespell
        exclude: >
          (?x)^(
              (.*\.(json|prisma|svg|yaml))|(local/js/yaml/index.*)|(app/utils/mustache/parser.js)|(app/ui/react-inspector/.*)|(api-ts/src/proxy/test-proxy/snapshots/.*)|(app/app/og/.*)|(brainstore/examples/.*)|(examples/.*)|(brainstore/(.*/)?Cargo\.lock)
          )$
        args:
          [
            "-L rouge,coo,couldn,unsecure,ot,crate,fo,afterall,ser,de,serde,doubleclick",
          ]
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.4.1
    hooks:
      - id: prettier
        exclude: ^(extension/|.*\.json$|pnpm-lock.yaml|app/pages/docs/cookbook/.*)|(examples/.+\.y(a)+ml)
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0
    hooks:
      - id: eslint
        # matches app/package.json command
        entry: "eslint --fix --max-warnings=0"
        exclude: |
          (?x)^(
            theme\.config\.jsx|
            forked/.*|
            api-ts/.*
          )$
        # https://pre-commit.com/#regular-expressions
        files: |
          (?x)^(
              ^(.*\.[jt]sx?)$
          )$
        types: [file]
        additional_dependencies:
          - "eslint@^8.56.0"
          - "@typescript-eslint/eslint-plugin@^8.10.0"
          - "eslint-config-next@^13.5.6"
          - "eslint-config-prettier@^9.1.0"
          - "eslint-plugin-tailwindcss@^3.14.0"
  - repo: local
    hooks:
      - id: sdk-docs
        name: sdk-docs
        entry: bash -c 'make docs-sdk'
        language: system
        files: ./sdk
  - repo: local
    hooks:
      - id: format-app-docs
        name: format-app-docs
        entry: ./scripts/format_app_docs.py
        language: system
        args: []
  - repo: local
    hooks:
      - id: cargo-fmt
        name: cargo-fmt
        entry: bash -c 'cd brainstore && cargo fmt --'
        language: system
        pass_filenames: true
