COGNITO_CLIENT_ID="72emfumal62st80q5llhafgcd0"
COGNITO_CLIENT_SECRET="125lpkvs0288oec00oi9o6p6tjqlllq2td1uf0q5la83st59q9rb"
COGNITO_ISSUER="https://cognito-idp.us-east-1.amazonaws.com/us-east-1_Oc3cNT6xj"
COGNITO_USER_POOL_ID="us-east-1_Oc3cNT6xj"
COGNITO_REGION="us-east-1"

NEXTAUTH_SECRET="ACg744QfG14IjZ8pVRRr2ufU6PoO95IHg9Dln7ozX4I="
NEXT_PUBLIC_SUPABASE_URL="http://127.0.0.1:54321"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"

SUPABASE_JWT_SECRET="super-secret-jwt-token-with-at-least-32-characters-long"
SUPABASE_PG_URL="postgresql://postgres:postgres@localhost:54322/postgres"

NEXT_PUBLIC_MULTI_TENANT_API_URL="http://127.0.0.1:8000"
NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL="ws://127.0.0.1:8788"

BLOB_READ_WRITE_TOKEN="vercel_blob_rw_duWDM5ZFF5xMbmWP_seehRwjW2Aw7cGxSnJYsRfv1ym4tUW"
SENTRY_IGNORE_API_RESOLUTION_ERROR=1

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_cXVhbGl0eS1tdXNrcmF0LTkwLmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_1nMWQtcSMroaM1tUAv0tDyUCv1qYs4c2w5Dwzz117J

# Private key for testing.
BRAINSTORE_AUTHORIZE_PRIVATE_KEY="27tmV9mAZ9EfYETl2mR6T6JP6EC6xUFb8J0lAzDGIt4="

# Use local redis as a proxy for upstash.
KV_REST_API_TOKEN="example_token"
KV_REST_API_URL="http://127.0.0.1:8079"

# ALlow testing-only cache operations.
ALLOW_TESTING_ONLY_CACHE_OPS=true
# Comment out to test rate limits
# KV_REST_API_READ_ONLY_TOKEN="As6HAAIgcDGQC8-7JX6lzR0ibJm9yf0KVWU-S3R8qIbNclTW2nnzcg"
# KV_URL="redis://default:<EMAIL>:6379"

# Uncomment to test out API-key auth.
# NEXT_PUBLIC_API_KEY_AUTH=1

# Uncomment to test out segment stuff
# NEXT_PUBLIC_SEGMENT_WRITE_KEY=DXzWrxnC7afAL3ilpJHj5JkhCmTUV3yl

# Uncomment to test out billing. These keys are for Stripe and Orb test mode.
# STRIPE_SECRET_KEY=<Look in the Engineering Vault in 1Password, credential named Stripe (test mode) Secret Key>
# ORB_API_KEY=<Look in the Engineering Vault in 1Password, credential named Orb (test mode) Staging API Credentials, TELEMETRY_TOKEN>
ORB_API_KEY=sk_not_a_real_api_key
ORB_WEBHOOK_SECRET=not_a_real_webhook_secret
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51Oxzoc00eSvg0zL8AkUTE1kwK907fJZ2VmdeC6Sw0HB4rffDzdZRWqQVxifNRRpqTb8HPuBVZ9kgp84Je4V343RO00TkGEGLKc


CSP_GLOBAL_POLICY="script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' {{#nonce}}'strict-dynamic' 'nonce-{{nonce}}'{{/nonce}} {{^nonce}}'unsafe-inline' https http{{/nonce}} *.js.stripe.com js.stripe.com maps.googleapis.com {{^nonce}}*.youtube.com api.segment.io cdn.segment.com clerk.braintrust.dev cdn.jsdelivr.net *.clerk.accounts.dev vercel.live tag.unifyintent.com *.googletagmanager.com buttons.github.io challenges.cloudflare.com googleads.g.doubleclick.net{{/nonce}}; style-src 'self' 'unsafe-inline' *.braintrust.dev; font-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/****************/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=development&sentry_release=5;"
CSP_GLOBAL_REPORT_ONLY_POLICY=""
CSP_ENABLED_ORG_NAMES="_unit_.+"
CSP_ENABLE_STRICT=true

CRON_SECRET=test-cron-secret-12345

NEXT_PUBLIC_HYPERTUNE_TOKEN=U2FsdGVkX19h4IlEi93VUaQZG+bRhmioF4eP0y0PL4o=
HYPERTUNE_FRAMEWORK=nextApp
HYPERTUNE_OUTPUT_DIRECTORY_PATH=generated
EXPERIMENTATION_CONFIG_ITEM_KEY="hypertune_5595"

NEXT_PUBLIC_ASHBY_API_KEY=e3ff5f1ed54aae90569395e02b641e799b94312925b2af576ff408e9899e4e26

# NOTE: this is used for data plane manager tests. Do not enable in any environment other than dev or CI.
TESTING_ONLY_ALLOW_SELF_HOSTED_DATA_PLANE=true

# Uncomment to convince the webapp that your local data plane is self-hosted
# Refer to api-ts/.env.development for how to run a self-hosted data plane locally.
# NEXT_PUBLIC_MULTI_TENANT_API_URL="http://127.0.0.1:1111"
# NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL="ws://127.0.0.1:1112"

# Datadog RUM
NEXT_PUBLIC_DATADOG_APPLICATION_ID=87f05671-7ca5-4fcb-bdde-32c13d3db63a
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN=pubdc69bd30773588f88fad4a74efe96424
NEXT_PUBLIC_DATADOG_SITE=us5.datadoghq.com
NEXT_PUBLIC_DATADOG_ENV=dev

# This is just for tests
BRAINTRUST_ENABLE_TEST_MODELS=true
