import { SqlQueryParams } from "#/utils/sql-query-params";
import { type AuthLookup } from "../_lookup_api_key";
import {
  commonFilterParamsSchema,
  genQueryFilters,
  paginationParamsSchema,
  splitPaginationParams,
  nullishSingleOrArraySchema,
  getObjects,
} from "../_object_crud_util";
import { userSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function sanitizeUserInfo(r: any) {
  return Object.fromEntries(
    Object.entries(r).filter((e) => e[0] !== "auth_id" && e[0] !== "clerk_id"),
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function sanitizeResults(results: any[]) {
  return results.map(sanitizeUserInfo);
}

export const userTypeEnum = z
  .enum(["user", "service_account"])
  .describe("The type of the user");
export type UserType = z.infer<typeof userTypeEnum>;

export const getHelperQueryParamsSchema = commonFilterParamsSchema
  .pick({
    id: true,
    org_name: true,
  })
  .merge(
    nullishSingleOrArraySchema(
      z.object({
        given_name: userSchema.shape.given_name.unwrap().unwrap(),
        family_name: userSchema.shape.family_name.unwrap().unwrap(),
        email: userSchema.shape.email.unwrap().unwrap(),
        user_type: userTypeEnum,
      }),
    ),
  );

export function getHelperQuery({
  queryParams,
  params,
  authLookup,
}: {
  queryParams: SqlQueryParams;
  params: z.infer<typeof getHelperQueryParamsSchema>;
  authLookup: AuthLookup;
}) {
  const { org_name, ...otherFilters } = params;
  return `
  with
  auth_user_orgs as (
      select distinct organizations.id
      from
          users
          join members on users.id = members.user_id
          join organizations on members.org_id = organizations.id
      where
          users.auth_id = ${queryParams.add(authLookup.auth_id)}
          and ${genQueryFilters({ tableName: "organizations" }, queryParams, { org_name }, authLookup)}
  )
  select *
  from users
  where
      exists (
          select 1
          from members
          where
              members.user_id = users.id
              and members.org_id in (select id from auth_user_orgs)
      )
      and ${genQueryFilters({ tableName: "users" }, queryParams, otherFilters)}
`;
}

export const getHelperParamsSchema = getHelperQueryParamsSchema.merge(
  paginationParamsSchema,
);

export const getHelperStripUserTypeParamsSchema = getHelperParamsSchema.omit({
  user_type: true,
});

export async function getHelper(
  params: z.infer<typeof getHelperParamsSchema>,
  authLookup: AuthLookup,
) {
  const { paginationParams, filters: paramsRest } =
    splitPaginationParams(params);
  const queryParams = new SqlQueryParams();
  const fullResultsQueryOverride = getHelperQuery({
    queryParams,
    params: paramsRest,
    authLookup,
  });

  return sanitizeResults(
    await getObjects({
      fullResultsQueryOverride,
      startingParams: queryParams,
      paginationParams,
      fullResultsSize: undefined,
    }),
  );
}

export async function getHelperStripUserType(
  params: z.infer<typeof getHelperParamsSchema>,
  authLookup: AuthLookup,
) {
  const results = await getHelper(params, authLookup);
  return results.map((result) => {
    const { user_type: _user_type, ...rest } = result;
    return rest;
  });
}
