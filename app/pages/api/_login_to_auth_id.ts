import { CognitoJwtVerifier } from "aws-jwt-verify";
import {
  lookupApi<PERSON><PERSON>,
  lookupUserId,
  findOrgId,
  isApi<PERSON>ey,
  redactedAuthToken,
  isServiceToken,
} from "./_lookup_api_key";
import { BT_IMPERSONATE_USER } from "@braintrust/core";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { IMPERSONATE_TOKEN_PREFIX } from "@braintrust/local";
import { ANON_AUTH_ID, ANON_USER_ID } from "#/utils/constants";
import { z } from "zod";
import { verifyToken } from "@clerk/nextjs/server";
import jwt from "jsonwebtoken";
import { type AuthLookup } from "./_lookup_api_key";

type GetHeaderFn = (x: string) => string | string[] | undefined | null;

function getHeaderWrapper(f: GetHeaderFn): (x: string) => string | undefined {
  function ret(x: string): string | undefined {
    const res = f(x);
    if (Array.isArray(res)) {
      return res.join(", ");
    } else {
      return res ?? undefined;
    }
  }
  return ret;
}

export function getAuthorizationToken(
  getHeaderRaw: (x: string) => string | string[] | undefined | null,
): string | undefined {
  const getHeader = getHeaderWrapper(getHeaderRaw);
  let token = getHeader("authorization")?.split(" ").slice(1).join(" ");
  if (!token || token.startsWith(IMPERSONATE_TOKEN_PREFIX)) {
    return token;
  }
  const impersonateUserId = getHeader(BT_IMPERSONATE_USER);
  if (token && impersonateUserId) {
    token = `${IMPERSONATE_TOKEN_PREFIX}${JSON.stringify({
      token: token,
      user_id: impersonateUserId,
    })}`;
  }
  return token;
}

export async function loginToAuthId({
  token: tokenRaw,
  org_name,
}: {
  token?: string;
  org_name?: string;
}): Promise<AuthLookup> {
  if (tokenRaw === undefined) {
    return { auth_id: ANON_AUTH_ID, user_id: ANON_USER_ID, org_id: null };
  }
  const { token, impersonateUserId } = (() => {
    let token = tokenRaw.trim();
    let impersonateUserId: string | undefined = undefined;
    if (token.startsWith(IMPERSONATE_TOKEN_PREFIX)) {
      const decomposed = JSON.parse(
        token.slice(IMPERSONATE_TOKEN_PREFIX.length),
      );
      token = decomposed.token;
      impersonateUserId = decomposed.user_id;
    }
    return { token, impersonateUserId };
  })();
  const authLookup = await (async () => {
    if (isApiKey(token) || isServiceToken(token)) {
      return await lookupApiKey({ key: token, org_name });
    }

    let isClerk = false;
    try {
      const decoded = jwt.decode(token);
      const parsed = z.object({ iss: z.string() }).parse(decoded);
      isClerk = parsed.iss.includes("clerk");
    } catch (e) {
      console.error(`Failed to decode JWT (${redactedAuthToken(token)})`, e);
    }

    let auth_id;
    if (isClerk) {
      try {
        const payload = await verifyToken(token, {
          secretKey: process.env.CLERK_SECRET_KEY,
        });
        auth_id = payload.publicMetadata?.bt_auth_id;
        if (!auth_id) {
          throw new HTTPError(401, "No bt_auth_id in public metadata");
        }
      } catch (e) {
        console.error("failed to validate clerk", e);
        throw new HTTPError(401, `Failed to validate Clerk JWT: ${e}`);
      }
    } else {
      // Verifier that expects valid access tokens:
      const verifier = CognitoJwtVerifier.create({
        userPoolId: process.env.COGNITO_USER_POOL_ID!,
        tokenUse: "id",
        clientId: process.env.COGNITO_CLIENT_ID!,
      });

      try {
        const payload = await verifier.verify(token);
        auth_id = payload.sub;
      } catch (e) {
        throw new HTTPError(401, `Failed to validate Cognito JWT: ${e}`);
      }
    }
    const user_id = await lookupUserId(auth_id);

    // No org_id associated with a JWT, but we have explicitly specified an
    // org name to disambiguate.
    let org_id: string | null = null;
    if (org_name) {
      org_id = await findOrgId({ org_name, auth_id });
      if (!org_id) {
        throw new HTTPError(
          403,
          "JWT does not belong to the org specified in org_name",
        );
      }
    }

    return { auth_id, user_id, org_id };
  })();
  if (!impersonateUserId) {
    return authLookup;
  }
  const impersonateIds = await getImpersonatingUserAuthId({
    origUserId: authLookup.user_id,
    impersonateUserId,
  });
  return {
    auth_id: impersonateIds.auth_id,
    user_id: impersonateIds.user_id,
    org_id: authLookup.org_id,
  };
}

// We can impersonate `impersonateUserId` if the authenticating user has an
// owner grant in *all* organizations that the `impersonateUserId` belongs to.
// Otherwise, we raise an error.
async function getImpersonatingUserAuthId({
  origUserId,
  impersonateUserId,
}: {
  origUserId: string;
  impersonateUserId: string;
}): Promise<{ user_id: string; auth_id: string }> {
  const conn = getServiceRoleSupabase();
  try {
    const { rows } = await conn.query(
      `
    with
        owner_role_id as (
            select get_owner_role_id() id
        ),
        impersonate_user_info as (
            select id, auth_id from users
            where id = uuid_or_null($2) or email = $2
            limit 1
        ),
        impersonate_user_orgs as (
            select members.org_id
            from members join impersonate_user_info on (
                members.user_id = impersonate_user_info.id
            )
        ),
        has_acls as (
            select
                (find_acl_id(
                    _object_type => 'organization',
                    _object_id => impersonate_user_orgs.org_id,
                    _user_id => $1,
                    _role_id => owner_role_id.id) is not null) has_acl
            from
                impersonate_user_orgs
                    left join owner_role_id on true
        )
        select id user_id, auth_id from impersonate_user_info
        where not (false in (select has_acl from has_acls))
    `,
      [origUserId, impersonateUserId],
    );
    if (rows.length === 1) {
      return z
        .object({ user_id: z.string(), auth_id: z.string() })
        .parse(rows[0]);
    }
  } catch (e) {
    console.error("Failed to fetch impersonating user ID", e);
  }
  throw new HTTPError(400, "Insufficient privileges to impersonate user");
}
