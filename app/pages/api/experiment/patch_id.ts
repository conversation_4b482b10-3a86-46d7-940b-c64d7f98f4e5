import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { patchObjects, extractSingularRow } from "../_object_crud_util";
import {
  experimentSchema,
  patchExperimentSchema,
} from "@braintrust/core/typespecs";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { isEmpty } from "#/utils/object";
import { additionalProjections } from "./_constants";
import { z } from "zod";
import { SqlQueryParams } from "#/utils/sql-query-params";

const paramsSchema = patchExperimentSchema.extend({
  id: z.union([z.string().uuid(), z.string().uuid().array().nonempty()]),
  shallow_replace_json_fields: z.string().array().nullish(),
  add_tags: z.string().array().nullish(),
  remove_tags: z.string().array().nullish(),
});

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  // Because the base 'experiments' table doesn't contain the 'public' field
  // anymore, we must manipulate it in a separate query.
  const supabasePool = getServiceRoleSupabase();
  const client = await supabasePool.connect();
  await client.query("begin");
  try {
    const {
      id: _id,
      shallow_replace_json_fields,
      metadata,
      repo_info,
      public: _public,
      add_tags,
      remove_tags,
      ...paramsRest
    } = params;

    // Validate that only one of tags, add_tags, or remove_tags is set
    const tagParamsCount = [params.tags, add_tags, remove_tags].filter(
      (param) => param !== undefined && param !== null,
    ).length;
    if (tagParamsCount > 1) {
      throw new HTTPError(
        400,
        "Cannot specify multiple tag parameters simultaneously. Use only one of: 'tags' (complete replacement), 'add_tags', or 'remove_tags'.",
      );
    }

    const queryParams = new SqlQueryParams();

    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    const patchValueParams: Record<string, any> = paramsRest;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    const patchJsonValueParams: Record<string, any> = {};
    Object.entries({ metadata, repo_info }).forEach(([k, v]) => {
      ((shallow_replace_json_fields ?? []).includes(k)
        ? patchValueParams
        : patchJsonValueParams)[k] = v;
    });

    // Normalize ID to array for consistent processing
    const ids: [string, ...string[]] = Array.isArray(params.id)
      ? params.id
      : [params.id];
    const isMultiple = Array.isArray(params.id);

    const tagsOverride: { tags?: string } = {};
    if (add_tags) {
      tagsOverride.tags = `(select array(select distinct unnested_tag from unnest(array_cat(coalesce(tags, '{}'), ${queryParams.add(add_tags)})) as unnested_tag))`;
    } else if (remove_tags) {
      tagsOverride.tags = `(select array(select unnested_tag from unnest(coalesce(tags, '{}')) as unnested_tag where unnested_tag not in (select unnest(${queryParams.add(remove_tags)}::TEXT[]))))`;
    }
    const patchedRows = await patchObjects(
      {
        authLookup,
        permissionInfo: {
          aclObjectType: "experiment",
        },
        filters: {
          id: ids,
        },
        finalResultSetAdditionalProjections: additionalProjections,
        patchValueParams,
        patchJsonValueParams,
        fullResultsSize: ids.length,
        patchExpressions: {
          ...tagsOverride,
        },
        startingParams: queryParams,
      },
      client,
    );

    // Process public field updates if needed
    const processedRows = [];
    for (const rawRow of patchedRows) {
      const patchedRow = z
        .object({
          public: z.boolean().nullish(),
        })
        .passthrough()
        .parse(rawRow);

      // If the caller wishes to change the 'public' param, we have to invoke a
      // separate UDF because it is not actually part of the experiment table.
      if (!isEmpty(params.public)) {
        const functionName = params.public
          ? "mark_experiment_as_public"
          : "mark_experiment_as_nonpublic";
        try {
          await client.query(
            `
                  select ${functionName}(experiment_id => experiments.id, project_id => experiments.project_id,
                                         performing_user_id => $1)
                  from experiments where id = $2
              `,
            [authLookup.user_id, patchedRow.id],
          );
          patchedRow["public"] = !!params.public;
        } catch (error) {
          console.error("Failed to change experiment public setting", error);
          throw new HTTPError(400, "Cannot change experiment's public setting");
        }
      }
      processedRows.push(patchedRow);
    }

    await client.query("commit");

    // For backward compatibility, return single object if single ID was provided
    if (!isMultiple) {
      return extractSingularRow({
        rows: processedRows,
        notFoundErrorMessage: undefined,
      });
    }

    return processedRows;
  } catch (e) {
    await client.query("rollback");
    throw e;
  } finally {
    await client.end();
  }
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: z.union([experimentSchema, experimentSchema.array()]),
  });
}
