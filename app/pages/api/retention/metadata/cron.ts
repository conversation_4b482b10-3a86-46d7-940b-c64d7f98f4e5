import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../../_request_util";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { extractSingularRow } from "../../_object_crud_util";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { HTTPError } from "#/utils/server-util";
import { z } from "zod";

const DEFAULT_BATCH_SIZE = 5000;

const paramsSchema = z.object({
  dry_run: z.boolean().default(false),
  batch_size: z.number().int().positive().optional(),
  org_id: z.union([z.string().uuid(), z.array(z.string().uuid())]).optional(),
});

const resultSchema = z.object({
  deleted_at: z.string().nullable(),
  experiment_count: z.coerce.number(),
  dataset_count: z.coerce.number(),
});

const outputSchema = z.object({
  dry_run: z.boolean(),
  num_processed_experiments: z.number(),
  num_deleted_experiments: z.number(),
  num_processed_datasets: z.number(),
  num_deleted_datasets: z.number(),
  deleted_at: z.string().nullable(),
});

/** This endpoint handles soft-deletion of experiment and dataset metadata from
 * the control plane according to any matching retention policies.
 *
 * Supports GET requests from Vercel Cron with no params
 *
 * Also supports manually triggered POST requests from sysadmins with a
 * few knobs
 */
async function handleRequest(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "POST") {
    await runJsonRequest(
      req,
      res,
      async (params, authLookup) => {
        const isSysadmin = await isAllowedSysadmin(authLookup);
        if (!isSysadmin) {
          throw new HTTPError(403, "Unauthorized");
        }
        return executeRetentionLogic(params);
      },
      {
        paramsSchema,
        outputSchema,
      },
    );
  } else {
    try {
      if (req.method !== "GET") {
        throw new HTTPError(401, "Bad request");
      }

      const isVercelCron = req.headers["user-agent"] === "vercel-cron/1.0";
      if (
        !isVercelCron ||
        req.headers["authorization"] !== `Bearer ${process.env.CRON_SECRET}`
      ) {
        throw new HTTPError(401, "Bad request");
      }

      // No parameters allowed for GET
      if (req.query && Object.keys(req.query).length > 0) {
        throw new HTTPError(401, "Parameters not allowed");
      }

      const result = await executeRetentionLogic({ dry_run: false });
      res.json(outputSchema.parse(result));
    } catch (error) {
      if (error instanceof HTTPError) {
        res.status(error.code).json({ error: error.message });
      } else {
        console.error("Retention cron error:", error);
        res.status(500).json({ error: "Internal server error" });
      }
    }
    res.end();
  }
}

function makeRetentionPoliciesCTE(
  queryParams: SqlQueryParams,
  orgIds: string[] | null,
) {
  const orgIdsParam = queryParams.add(orgIds);
  return `
    retention_policies AS (
      SELECT
        pa.project_id,
        pa.object_type,
        NOW() - ((pa.config->>'retention_days')::int * INTERVAL '1 day') as cutoff_date
      FROM project_automations pa
      INNER JOIN projects p ON pa.project_id = p.id
      WHERE pa.event_type = 'retention'
        AND (${orgIdsParam}::uuid[] IS NULL OR p.org_id = ANY(${orgIdsParam}::uuid[]))
    )`;
}

function makeEligibleExperimentsCTE(
  queryParams: SqlQueryParams,
  limit?: number,
) {
  const limitParam = limit ? queryParams.add(limit) : undefined;
  const limitClause = limitParam ? `LIMIT ${limitParam}` : "";
  return `
    eligible_experiments AS (
      SELECT e.id
      FROM experiments e
      INNER JOIN retention_policies rp ON
        rp.project_id = e.project_id AND rp.object_type = 'experiment'
      WHERE e.deleted_at IS NULL AND e.created < rp.cutoff_date
      ORDER BY e.id
      ${limitClause}
    )`;
}

function makeEligibleDatasetsCTE(queryParams: SqlQueryParams, limit?: number) {
  const limitParam = limit ? queryParams.add(limit) : undefined;
  const limitClause = limitParam ? `LIMIT ${limitParam}` : "";
  return `
    eligible_datasets AS (
      SELECT d.id
      FROM datasets d
      INNER JOIN retention_policies rp ON
        rp.project_id = d.project_id AND rp.object_type = 'dataset'
      WHERE d.deleted_at IS NULL AND d.created < rp.cutoff_date
      ORDER BY d.id
      ${limitClause}
    )`;
}

async function executeRetentionLogic(params: z.infer<typeof paramsSchema>) {
  const supabase = getServiceRoleSupabase();

  const orgIds = params.org_id
    ? Array.isArray(params.org_id)
      ? params.org_id
      : [params.org_id]
    : null;

  const batchSize = params.batch_size || DEFAULT_BATCH_SIZE;

  // This query was benchmarked on the control plane in dry_run mode and ran quickly
  // due to the very small (<10) number of retention policies. We can monitor the
  // performance of this as the number of policies and the number of matching objects
  // to be deleted grows and optimize the query or reduce the batch size further.
  const queryParams = new SqlQueryParams();
  const query = `
    WITH ${makeRetentionPoliciesCTE(queryParams, orgIds)},
    ${makeEligibleExperimentsCTE(queryParams, batchSize)},
    ${makeEligibleDatasetsCTE(queryParams, batchSize)}
    ${
      params.dry_run
        ? `
      select
        null as deleted_at,
        (select count(*) from eligible_experiments) as experiment_count,
        (select count(*) from eligible_datasets) as dataset_count;
      `
        : `,
      deleted_experiments as (
        UPDATE experiments
        SET deleted_at = NOW()
        FROM eligible_experiments
        WHERE experiments.id = eligible_experiments.id
        RETURNING 1
      ),
      deleted_datasets as (
        UPDATE datasets
        SET deleted_at = NOW()
        FROM eligible_datasets
        WHERE datasets.id = eligible_datasets.id
        RETURNING 1
      )
      select
        NOW() as deleted_at,
        (select count(*) from deleted_experiments) as experiment_count,
        (select count(*) from deleted_datasets) as dataset_count;
      `
    }`;

  const row = extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage: undefined,
  });

  const { experiment_count, dataset_count, deleted_at } =
    resultSchema.parse(row);

  return {
    dry_run: params.dry_run,
    num_processed_experiments: experiment_count,
    num_deleted_experiments: params.dry_run ? 0 : experiment_count,
    num_processed_datasets: dataset_count,
    num_deleted_datasets: params.dry_run ? 0 : dataset_count,
    deleted_at,
  };
}

export default handleRequest;
