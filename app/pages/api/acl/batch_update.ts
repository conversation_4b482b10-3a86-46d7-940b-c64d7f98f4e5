import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import type { NextApiRequest, NextApiResponse } from "next";
import { type AuthLookup } from "../_lookup_api_key";
import { runJsonRequest } from "../_request_util";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "../_object_crud_util";
import { cleanupAclJson } from "./_util";
import {
  type AclBatchUpdateRequest,
  type AclBatchUpdateResponse,
  type AclObjectType,
  aclObjectTypeEnum,
  aclBatchUpdateRequestSchema,
  aclBatchUpdateResponseSchema,
  permissionEnum,
} from "@braintrust/core/typespecs";
import { formatNotFoundErrorMessage } from "@braintrust/local";
import { type BtPgClient } from "@braintrust/local/bt-pg";
import { z } from "zod";

const _multiAclItemSchema = z.strictObject({
  user_id: z.string().nullish(),
  group_id: z.string().nullish(),
  permission: permissionEnum.nullish(),
  role_id: z.string().nullish(),
  restrict_object_type: aclObjectTypeEnum.nullish(),
});

type MultiAclItem = z.infer<typeof _multiAclItemSchema>;

type MutateAclsInput = {
  object_type: AclObjectType;
  object_id: string;
  acls: MultiAclItem[];
  authLookup: AuthLookup;
  client: BtPgClient;
};

const aclBatchUpdateQueryResponseSchema = z.object({
  output: z.object({
    objects_exist_and_has_permission: z.unknown(),
    acls: z.record(z.unknown()).array(),
  }),
});

const aclBatchUpdateResponseAugmentedSchema =
  aclBatchUpdateResponseSchema.extend({
    objects_exist_and_has_permission: z.boolean(),
  });

type AclBatchUpdateResponseAugmented = z.infer<
  typeof aclBatchUpdateResponseAugmentedSchema
>;

async function addAcls({
  object_type,
  object_id,
  acls,
  authLookup,
  client,
}: MutateAclsInput): Promise<AclBatchUpdateResponseAugmented> {
  const { query: fullResultsSubquery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: object_type,
      aclPermission: "create_acls",
    },
    filters: {
      id: [object_id],
    },
  });

  const userIdParams = acls.map((x) => queryParams.add(x.user_id)).join(", ");
  const groupIdParams = acls.map((x) => queryParams.add(x.group_id)).join(", ");
  const permissionParams = acls
    .map((x) => queryParams.add(x.permission))
    .join(", ");
  const roleIdParams = acls.map((x) => queryParams.add(x.role_id)).join(", ");
  const restrictObjectTypeParams = acls
    .map((x) => queryParams.add(x.restrict_object_type))
    .join(", ");

  const query = `
    with
    objects_exist_and_has_permission as (
        select exists(${fullResultsSubquery}) as value
    ),
    acl_results as (
        select register_acl_unchecked(
            _object_type => ${queryParams.add(object_type)},
            _object_id => ${queryParams.add(object_id)},
            _user_id => params.user_id,
            _group_id => params.group_id,
            _permission => params.permission,
            _role_id => params.role_id,
            _restrict_object_type => params.restrict_object_type)->'acl' result
        from
        unnest(
            array [${userIdParams}] :: uuid[],
            array [${groupIdParams}] :: uuid[],
            array [${permissionParams}] :: permission_type[],
            array [${roleIdParams}] :: uuid[],
            array [${restrictObjectTypeParams}] :: acl_object_type[]
        ) params(user_id, group_id, permission, role_id, restrict_object_type)
        join objects_exist_and_has_permission on objects_exist_and_has_permission.value
    )
    select
        jsonb_build_object(
            'objects_exist_and_has_permission', (select value from objects_exist_and_has_permission),
            'acls', (select coalesce(jsonb_agg(result order by (result->>'id')::uuid), '[]'::jsonb) from acl_results)
        ) output
  `;

  const row = extractSingularRow({
    rows: (await client.query(query, queryParams.params)).rows,
    notFoundErrorMessage: undefined,
  });
  const rowParsed = aclBatchUpdateQueryResponseSchema.parse(row);
  return aclBatchUpdateResponseAugmentedSchema.parse({
    objects_exist_and_has_permission:
      rowParsed.output.objects_exist_and_has_permission,
    added_acls: rowParsed.output.acls.map(cleanupAclJson),
    removed_acls: [],
  });
}

async function removeAcls({
  object_type,
  object_id,
  acls,
  authLookup,
  client,
}: MutateAclsInput): Promise<AclBatchUpdateResponseAugmented> {
  const { query: fullResultsSubquery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: object_type,
      aclPermission: "delete_acls",
    },
    filters: {
      id: [object_id],
    },
  });

  const userIdParams = acls.map((x) => queryParams.add(x.user_id)).join(", ");
  const groupIdParams = acls.map((x) => queryParams.add(x.group_id)).join(", ");
  const permissionParams = acls
    .map((x) => queryParams.add(x.permission))
    .join(", ");
  const roleIdParams = acls.map((x) => queryParams.add(x.role_id)).join(", ");
  const restrictObjectTypeParams = acls
    .map((x) => queryParams.add(x.restrict_object_type))
    .join(", ");

  const query = `
    with
    objects_exist_and_has_permission as (
        select exists(${fullResultsSubquery}) as value
    ),
    found_acl_ids as (
        select find_acl_id(
            _object_type => ${queryParams.add(object_type)},
            _object_id => ${queryParams.add(object_id)},
            _user_id => params.user_id,
            _group_id => params.group_id,
            _permission => params.permission,
            _role_id => params.role_id,
            _restrict_object_type => params.restrict_object_type) id
        from
        unnest(
            array [${userIdParams}] :: uuid[],
            array [${groupIdParams}] :: uuid[],
            array [${permissionParams}] :: permission_type[],
            array [${roleIdParams}] :: uuid[],
            array [${restrictObjectTypeParams}] :: acl_object_type[]
        ) params(user_id, group_id, permission, role_id, restrict_object_type)
        join objects_exist_and_has_permission on objects_exist_and_has_permission.value
    ),
    deletion_results as (
        delete from acls
        where id in (select id from found_acl_ids)
        returning *
    )
    select
        jsonb_build_object(
            'objects_exist_and_has_permission', (select value from objects_exist_and_has_permission),
            'acls', (select coalesce(jsonb_agg(deletion_results order by deletion_results.id), '[]'::jsonb) from deletion_results)
        ) output
  `;

  const row = extractSingularRow({
    rows: (await client.query(query, queryParams.params)).rows,
    notFoundErrorMessage: undefined,
  });
  const rowParsed = aclBatchUpdateQueryResponseSchema.parse(row);
  return aclBatchUpdateResponseAugmentedSchema.parse({
    objects_exist_and_has_permission:
      rowParsed.output.objects_exist_and_has_permission,
    added_acls: [],
    removed_acls: rowParsed.output.acls.map(cleanupAclJson),
  });
}

export async function batchUpdateHelper(
  params: AclBatchUpdateRequest,
  authLookup: AuthLookup,
): Promise<AclBatchUpdateResponse | null> {
  const { add_acls, remove_acls } = params;

  // Group the ACLs we want to add and remove by (object_type, object_id,
  // action).
  const objectToMutateAclsInputAndAction: Record<
    string,
    {
      object_type: AclObjectType;
      object_id: string;
      acls: MultiAclItem[];
      action: "add" | "remove";
    }
  > = {};
  for (const obj of add_acls ?? []) {
    const { object_type, object_id, ...acl } = obj;
    const key = [object_type, object_id, "add"].join(";");
    if (!(key in objectToMutateAclsInputAndAction)) {
      objectToMutateAclsInputAndAction[key] = {
        object_type,
        object_id,
        action: "add",
        acls: [],
      };
    }
    objectToMutateAclsInputAndAction[key].acls.push(acl);
  }
  for (const obj of remove_acls ?? []) {
    const { object_type, object_id, ...acl } = obj;
    const key = [object_type, object_id, "remove"].join(";");
    if (!(key in objectToMutateAclsInputAndAction)) {
      objectToMutateAclsInputAndAction[key] = {
        object_type,
        object_id,
        action: "remove",
        acls: [],
      };
    }
    objectToMutateAclsInputAndAction[key].acls.push(acl);
  }

  const supabasePool = getServiceRoleSupabase();
  const client = await supabasePool.connect();
  await client.query("begin");
  try {
    const result = await (async (): Promise<AclBatchUpdateResponse | null> => {
      // Run each action and collect the results.
      const results: AclBatchUpdateResponseAugmented[] = [];
      for (const actionSpec of Object.values(
        objectToMutateAclsInputAndAction,
      )) {
        const { object_type, object_id, action, acls } = actionSpec;
        const input: MutateAclsInput = {
          object_type,
          object_id,
          acls,
          authLookup,
          client,
        };
        const result = await (action === "add"
          ? addAcls(input)
          : removeAcls(input));
        results.push(result);
      }

      // If no operations ran, return a blank output.
      if (results.length === 0) {
        return null;
      }

      // Otherwise, combine all permission checks (ignoring nulls).
      const objects_exist_and_has_permission = results.every(
        (x) => !!x.objects_exist_and_has_permission,
      );
      if (!objects_exist_and_has_permission) {
        throw new HTTPError(
          400,
          formatNotFoundErrorMessage({
            permission: "create/delete_acls",
            objectTypeAndIds: Object.values(
              objectToMutateAclsInputAndAction,
            ).map((x) => [x.object_type, x.object_id]),
          }),
        );
      }

      // All of the operations succeeded with permissions. Combine the results.
      return results.reduce(
        (acc: AclBatchUpdateResponse, r) => ({
          added_acls: acc.added_acls.concat(r.added_acls),
          removed_acls: acc.removed_acls.concat(r.removed_acls),
        }),
        {
          added_acls: [],
          removed_acls: [],
        },
      );
    })();
    await client.query(result ? "commit" : "rollback");
    return result;
  } catch (e) {
    await client.query("rollback");
    throw e;
  } finally {
    await client.end();
  }
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, batchUpdateHelper, {
    paramsSchema: aclBatchUpdateRequestSchema,
    outputSchema: aclBatchUpdateResponseSchema.nullable(),
  });
}
