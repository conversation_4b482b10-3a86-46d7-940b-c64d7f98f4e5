import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import {
  idParamSchema,
  extractSingularRow,
  getObjects,
  patchObjects,
} from "../_object_crud_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { groupSchema, patchGroupSchema } from "@braintrust/core/typespecs";
import { additionalProjections } from "./_constants";
import { type z } from "zod";

const paramsSchema = idParamSchema.merge(patchGroupSchema);

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  // Because patching occurs across multiple tables, we run everything in a
  // transaction within our own pool client.
  const supabasePool = getServiceRoleSupabase();
  const client = await supabasePool.connect();
  await client.query("begin");
  try {
    const {
      id,
      add_member_users,
      remove_member_users,
      add_member_groups,
      remove_member_groups,
      ...paramsRest
    } = params;
    extractSingularRow({
      rows: await patchObjects(
        {
          authLookup,
          permissionInfo: {
            aclObjectType: "group",
          },
          filters: {
            id: [params.id],
          },
          patchValueParams: paramsRest,
          fullResultsSize: 1,
        },
        client,
      ),
      notFoundErrorMessage: undefined,
    });
    // Update the users and inheritors. Since the previous query succeeded, we
    // know we have the appropriate permissions.
    if (add_member_users?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const userParams = add_member_users
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
              insert into group_users(group_id, user_id)
              select ${idParam}, user_id from unnest(array [${userParams}]::uuid[]) user_id
              on conflict do nothing
              `,
        queryParams.params,
      );
    }
    if (remove_member_users?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const userParams = remove_member_users
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
            delete from group_users
            where group_id = ${idParam} and user_id in (${userParams})
          `,
        queryParams.params,
      );
    }
    if (add_member_groups?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const groupParams = add_member_groups
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
              insert into group_members(group_id, member_group_id)
              select ${idParam}, member_group_id from unnest(array [${groupParams}]::uuid[]) member_group_id
              on conflict do nothing
              `,
        queryParams.params,
      );
    }
    if (remove_member_groups?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const groupParams = remove_member_groups
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
            delete from group_members
            where group_id = ${idParam} and member_group_id in (${groupParams})
          `,
        queryParams.params,
      );
    }
    const result = extractSingularRow({
      rows: await getObjects(
        {
          authLookup,
          permissionInfo: {
            aclObjectType: "group",
          },
          filters: {
            id,
          },
          finalResultSetAdditionalProjections: additionalProjections,
          fullResultsSize: 1,
        },
        client,
      ),
      notFoundErrorMessage: undefined,
    });
    await client.query("commit");
    return result;
  } catch (e) {
    await client.query("rollback");
    throw e;
  } finally {
    await client.end();
  }
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: groupSchema,
  });
}
