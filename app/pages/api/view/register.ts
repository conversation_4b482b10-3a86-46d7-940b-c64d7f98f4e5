import { z } from "zod";
import { getServiceRoleSupabase } from "#/utils/supabase";
import type { NextApiRequest, NextApiResponse } from "next";
import { type AuthLookup } from "../_lookup_api_key";
import { runJsonRequest } from "../_request_util";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "../_object_crud_util";
import { createViewSchema } from "@braintrust/core/typespecs";
import { viewMetadataSchema } from "@braintrust/local/app-schema";

// `view_data` is stored in the API `views` table instead of supabase.
const paramsSchema = createViewSchema
  .omit({ name: true, view_data: true })
  .extend({
    view_name: z.string(),
    view_data_id: z.string().uuid(),
    update: z.boolean().nullish(),
  });

const registerViewResponseSchema = z.strictObject({
  view: viewMetadataSchema,
  found_existing: z.boolean(),
});

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const {
    query: viewFullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: params.object_type,
      aclPermission: "update",
    },
    filters: {
      id: [params.object_id],
    },
  });

  const query = `
    with
    objects_exist_and_has_permission as (
        select exists(${viewFullResultsSubquery}) as value
    )
    select register_view_unchecked(
        auth_id => ${queryParams.add(authLookup.auth_id)},
        object_type => ${queryParams.add(params.object_type)},
        object_id => ${queryParams.add(params.object_id)},
        view_type => ${queryParams.add(params.view_type)},
        name => ${queryParams.add(params.view_name)},
        view_data_id => ${queryParams.add(params.view_data_id)},
        options => ${queryParams.add(params.options)},
        update => ${queryParams.add(params.update)}
    ) output
    from objects_exist_and_has_permission
    where objects_exist_and_has_permission.value
  `;

  const supabase = getServiceRoleSupabase();
  return extractSingularRow({
    rows: (await supabase.query(query, queryParams.params)).rows,
    notFoundErrorMessage,
  })["output"];
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: registerViewResponseSchema,
  });
}
