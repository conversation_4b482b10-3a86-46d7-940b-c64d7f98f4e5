import { z } from "zod";
import {
  commonFilterParamsSchema,
  genQueryFilters,
  idParamSchema,
  makeFullResultSetQuery,
  nullishSingleOrArraySchema,
} from "../_object_crud_util";
import { type Permission } from "@braintrust/core/typespecs";
import { aclObjectParamsSchema } from "../acl/_util";
import { type AuthLookup } from "../_lookup_api_key";
import { viewTypeEnum } from "@braintrust/core/typespecs";

export const viewIdParamsSchema = idParamSchema.merge(aclObjectParamsSchema);

export const viewFilterParamsSchema = nullishSingleOrArraySchema(
  z.object({
    view_type: viewTypeEnum,
  }),
).merge(
  commonFilterParamsSchema.pick({
    name: true,
    id: true,
  }),
);

type MakeViewFullResultsQueryInput = z.infer<typeof aclObjectParamsSchema> &
  z.infer<typeof viewFilterParamsSchema> & {
    authLookup: AuthLookup;
    aclPermission: Permission;
  };

export function makeViewFullResultsQuery({
  authLookup,
  object_type,
  object_id,
  aclPermission,
  view_type,
  name,
  id,
}: MakeViewFullResultsQueryInput) {
  const {
    query: viewFullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: object_type,
      aclPermission,
    },
    filters: {
      id: [object_id],
    },
  });

  const viewFullResultsQuery = `
    select * from views
    where
        views.object_id in (select id from (${viewFullResultsSubquery}) "t")
        and ${genQueryFilters({ tableName: "views" }, queryParams, { object_type, view_type, name, id })}
        and views.object_type = ${queryParams.add(object_type)}
        and views.deleted_at isnull
  `;

  return {
    viewFullResultsQuery,
    viewFullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  };
}
