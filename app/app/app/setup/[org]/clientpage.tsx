"use client";

import { redirect, useSearchParams } from "next/navigation";
import { GetStarted } from "./get-started";

export function ClientPage({ orgName }: { orgName: string }) {
  const searchParams = useSearchParams();

  const fromPlayground = searchParams?.get("referrer") === "playground";

  if (fromPlayground) {
    return redirect(`/setup/${orgName}/providers`);
  }

  return <GetStarted orgName={orgName} />;
}
