"use client";
import { <PERSON><PERSON><PERSON> } from "../../../[org]/onboarding-logos";
import { TypescriptLogo } from "../../../[org]/onboarding-logos";

import { useState, useEffect, useRef, useMemo } from "react";
import { isObject } from "@braintrust/core";
import { useIndexedDBObject } from "#/utils/use-indexeddb-object";
import { useCreateNewPlayground } from "#/ui/prompts/function-editor/create-playground";
import { type LoggedOutPlaygroundData } from "#/app/playground/logged-out-playground-table";
import Link from "next/link";
import { getProjectLink } from "../../../[org]/p/[project]/getProjectLink";
import { buttonVariants } from "#/ui/button";

enum JsPackageManager {
  PNPM = "pnpm",
  NPM = "npm",
  YARN = "yarn",
}

enum PythonInstaller {
  PIP = "pip",
  UV = "uv",
}

import CodeToCopy from "#/ui/code-to-copy";
import { Spinner } from "#/ui/icons/spinner";
import { createApiKey } from "#/app/app/[org]/settings/api-keys/api-key";
import {
  type fetchApiKeys,
  type deleteApiKey,
} from "#/app/app/[org]/settings/api-keys/actions";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { toast } from "sonner";
import { useAnalytics } from "#/ui/use-analytics";
import { Tabs, TabsList, TabsTrigger } from "#/ui/tabs";
import { getProviderIcon } from "../../../[org]/settings/secrets/utils";
import {
  getCurrentCodeExample,
  Language,
  SdkIntegration,
  getIntegrationConfigs,
  starterProjectName,
  VercelFramework,
} from "./trace-config";
import { Button } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { type getProjectSummary } from "../../../[org]/org-actions";
import { useUser } from "#/utils/user";
import { ArrowUpRight } from "lucide-react";
import { motion } from "motion/react";

const getLanguageIcon = (name: string, size = 24) => {
  if (name.toLowerCase().startsWith("python")) {
    return <PythonLogo size={size} />;
  }
  if (name.toLowerCase().startsWith("typescript")) {
    return <TypescriptLogo size={size} />;
  }
};

const isProviderLangDisabled = (
  provider: SdkIntegration,
  language: Language,
): boolean => {
  const providerConfig = getIntegrationConfigs()[provider];
  return !providerConfig?.codeExamples?.[language];
};

export const SetupTracing = ({ orgName }: { orgName: string }) => {
  const { orgs } = useUser();
  const org = orgs[orgName];

  const { analytics } = useAnalytics();

  const [lang, setLang] = useState<Language>(Object.values(Language)[0]);
  const [llmProvider, setLlmProvider] = useState<SdkIntegration>(
    Object.values(SdkIntegration)[0],
  );
  const [jsPackageManager, setJsPackageManager] = useState<JsPackageManager>(
    JsPackageManager.PNPM,
  );
  const [pythonInstaller, setPythonInstaller] = useState<PythonInstaller>(
    PythonInstaller.PIP,
  );
  const [framework, setFramework] = useState<VercelFramework>(
    VercelFramework.NEXT,
  );
  const [creatingApiKey, setCreatingApiKey] = useState(false);
  const [apiKey, setApiKey] = useState<string | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [starterProjectId, setStarterProjectId] = useState<
    string | undefined
  >();

  const apiUrl = org.api_url;

  const { getToken } = useAuth();
  //TODO: verify we need this, avoid calling endpoints twice
  const hasCreatedApiKey = useRef(false);

  const [loggedOutPlaygroundData, _, clearLoggedOutPlaygroundData] =
    useIndexedDBObject<LoggedOutPlaygroundData>({
      store: "loggedOutData",
      key: "playground",
    });

  const { createNewPlayground } = useCreateNewPlayground({
    orgName,
    projectName: starterProjectName,
    onFinish: () => {
      clearLoggedOutPlaygroundData();
    },
    routerMethod: "replace",
    orgId: org.id,
  });

  const integrationsSectionRef = useRef<HTMLDivElement | null>(null);
  const instructionsContainerRef = useRef<HTMLDivElement | null>(null);

  const providers = useMemo(() => Object.values(SdkIntegration), []);

  const handlePlaygroundCreate = async () => {
    if (!org.id) {
      toast.error("Failed to create playground", {
        action: (
          <Link
            href={getProjectLink({
              orgName,
              projectName: starterProjectName,
            })}
            className={buttonVariants({ size: "xs" })}
          >
            Go to project
          </Link>
        ),
      });
      return;
    }
    try {
      setIsSubmitting(true);
      {
        try {
          await createNewPlayground({
            projectId: starterProjectId,
            sessionName: "Playground 1",
            datasetName: "Dataset 1",
            initialRecords: loggedOutPlaygroundData?.playgroundBlocks.map(
              ({ prompt_data, function_data }) => ({
                prompt_data,
                function_data,
              }),
            ),
            datasetRows: loggedOutPlaygroundData?.datasetRows.map(
              ({ input, expected, metadata }) => ({
                input,
                expected,
                metadata: isObject(metadata) ? metadata : undefined,
              }),
            ),
            scorerFunctions: loggedOutPlaygroundData?.scorerFunctions,
            promptSessionData: {
              scorers: loggedOutPlaygroundData?.savedScorers ?? [],
              settings: loggedOutPlaygroundData?.playgroundSettings,
            },
          });
        } catch (error) {
          console.error(error);
          toast.error("Failed to create playground", {
            action: (
              <Link
                href={getProjectLink({
                  orgName,
                  projectName: starterProjectName,
                })}
                className={buttonVariants({ size: "xs" })}
              >
                Go to project
              </Link>
            ),
          });
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to create project. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const providerConfigs = getIntegrationConfigs(
    apiKey,
    starterProjectId,
    apiUrl,
  );
  const selectedProviderConfig = providerConfigs[llmProvider];
  const codeExample = getCurrentCodeExample(llmProvider, lang, framework);
  const providerCodeExamples = codeExample?.snippets ?? [];

  useEffect(() => {
    if (isProviderLangDisabled(llmProvider, lang)) {
      setLang(selectedProviderConfig.supportedLanguages[0]);
    }
  }, [lang, llmProvider, selectedProviderConfig.supportedLanguages]);

  // Fetch starter project ID for providers that need it
  useEffect(() => {
    if (!orgName) return;

    const fetchStarterProjectId = async () => {
      try {
        const projects = await invokeServerAction<typeof getProjectSummary>({
          fName: "getProjectSummary",
          args: { org_name: orgName },
          getToken,
        });

        const starterProject = projects?.find(
          (p) => p.project_name === starterProjectName,
        );
        if (starterProject) {
          setStarterProjectId(starterProject.project_id);
        }
      } catch (error) {
        console.warn("Failed to fetch starter project ID:", error);
      }
    };
    fetchStarterProjectId();
  }, [orgName, getToken]);

  useEffect(() => {
    if (!org.id || hasCreatedApiKey.current) return;

    const createAndReplaceTutorialApiKey = async () => {
      hasCreatedApiKey.current = true;
      setCreatingApiKey(true);

      const existingApiKeys =
        (await invokeServerAction<typeof fetchApiKeys>({
          fName: "fetchApiKeys",
          args: {},
          getToken,
        })) ?? [];

      const tutorialKeys = existingApiKeys.filter(
        (key) => key.name === "tutorial" && key.org_id === org.id,
      );

      try {
        const newKey = await createApiKey({
          name: "tutorial",
          orgId: org.id ?? "",
        });
        setApiKey(newKey);
        analytics?.track("onboarding_api_key_created", { orgName });
      } catch (error) {
        hasCreatedApiKey.current = false;
        toast.error("Failed to create API key", {
          description: String(error),
        });
      } finally {
        setCreatingApiKey(false);
      }

      try {
        await Promise.all(
          tutorialKeys.map((tutorialKey) =>
            invokeServerAction<typeof deleteApiKey>({
              fName: "deleteApiKey",
              args: { api_key_id: tutorialKey.id },
              getToken,
            }),
          ),
        );
      } catch (error) {
        toast.error("Failed to delete old tutorial keys", {
          description: String(error),
        });
      }
    };

    createAndReplaceTutorialApiKey();
  }, [org.id, orgName, analytics, getToken]);

  return (
    <div className="mx-auto flex size-full flex-col gap-8">
      <motion.div
        key="waiting-for-logs"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          delay: 0.3,
          ease: "easeOut",
        }}
        className="pointer-events-none fixed inset-x-0 top-0 z-50 flex justify-center py-9"
      >
        <div className="-mb-1 flex items-center gap-2 rounded-full py-1 pl-2 pr-3 text-[15px] font-medium shadow-lg bg-accent-600 text-background">
          <Spinner className="size-5" />
          <span className="animate-textShimmer bg-gradient-to-r from-accent-50 via-accent-300 to-accent-50 bg-clip-text text-transparent">
            Waiting for logs
          </span>
        </div>
      </motion.div>
      <div className="flex flex-col">
        <div>
          <h1 className="mb-2 font-display text-3xl font-semibold">
            Trace LLM calls to set up observability
          </h1>
          <p className="text-base text-primary-600">
            Start tracing your existing app in minutes. You will be redirected
            to the dashboard once we receive your first log.
          </p>
        </div>
      </div>

      <div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            opacity: {
              duration: 0.3,
              ease: "easeOut",
            },
          }}
          className="text-sm font-medium text-primary-700"
        >
          Providers and frameworks
        </motion.div>
        <div
          ref={integrationsSectionRef}
          className="group relative scroll-mt-8"
        >
          <div className="grid grid-cols-4 gap-2 py-2 md:grid-cols-5">
            {providers.map((provider, index) => {
              const providerConfig = providerConfigs[provider];
              const isSelected = llmProvider === provider;
              const providerIcon = getProviderIcon(provider, 36);
              return (
                <motion.div
                  key={provider}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  whileHover={{ y: 2 }}
                  transition={{
                    opacity: {
                      duration: 0.2,
                      ease: "easeOut",
                      delay: (index + 1) * 0.02,
                    },
                    y: {
                      duration: 0.2,
                      ease: "easeOut",
                      delay: 0,
                    },
                  }}
                  className="flex w-full flex-none"
                >
                  <Button
                    className={cn(
                      "flex-1 px-0 text-xs flex flex-col items-center snap-center border",
                      { "border-primary-500 bg-background": isSelected },
                    )}
                    onClick={() => {
                      setLlmProvider(provider);
                      integrationsSectionRef.current?.scrollIntoView({
                        behavior: "smooth",
                        block: "start",
                      });
                    }}
                  >
                    {providerConfig.icon ?? providerIcon}
                    <span className="text-xs">
                      {providerConfig?.name ?? provider}
                    </span>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          opacity: {
            duration: 0.4,
            ease: "easeOut",
            delay: (providers.length + 4) * 0.02,
          },
        }}
        ref={instructionsContainerRef}
        className="group relative scroll-mt-2"
      >
        <div className="h-8">
          <Tabs value={lang}>
            <TabsList className="rounded-b-none">
              {Object.values(Language).map((language) => {
                const isDisabled = isProviderLangDisabled(
                  llmProvider,
                  language,
                );
                if (!isDisabled) {
                  return (
                    <TabsTrigger
                      key={language}
                      value={language}
                      className="flex h-6 items-center gap-1 text-xs !shadow-none data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=inactive]:text-primary-500"
                      onClick={() => setLang(language)}
                    >
                      {getLanguageIcon(language, 16)}
                      {language}
                    </TabsTrigger>
                  );
                }
              })}
            </TabsList>
          </Tabs>
        </div>
        <div className="relative rounded-lg rounded-tl-none p-6 bg-primary-100">
          <div className="flex flex-col gap-6">
            <div>
              <div className="mb-2 flex items-end gap-3">
                <p className="flex h-6 flex-1 items-end text-sm">
                  Install required dependencies
                </p>
                <div className="h-6">
                  {lang === Language.TYPESCRIPT && (
                    <Tabs value={jsPackageManager}>
                      <TabsList className="h-6">
                        {Object.values(JsPackageManager).map(
                          (packageManager) => (
                            <TabsTrigger
                              key={packageManager}
                              value={packageManager}
                              className="h-5 text-xs"
                              onClick={() => {
                                setJsPackageManager(packageManager);
                                instructionsContainerRef.current?.scrollIntoView(
                                  {
                                    behavior: "smooth",
                                    block: "start",
                                  },
                                );
                              }}
                            >
                              {packageManager}
                            </TabsTrigger>
                          ),
                        )}
                      </TabsList>
                    </Tabs>
                  )}
                  {lang === Language.PYTHON && (
                    <Tabs value={pythonInstaller}>
                      <TabsList className="h-6">
                        {Object.values(PythonInstaller).map((installer) => (
                          <TabsTrigger
                            key={installer}
                            value={installer}
                            className="h-5 text-xs"
                            onClick={() => {
                              setPythonInstaller(installer);
                              instructionsContainerRef.current?.scrollIntoView({
                                behavior: "smooth",
                                block: "start",
                              });
                            }}
                          >
                            {installer}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </Tabs>
                  )}
                </div>
              </div>
              <CodeToCopy
                highlighterClassName="bg-background"
                data={(() => {
                  const codeExample = getCurrentCodeExample(
                    llmProvider,
                    lang,
                    framework,
                  );
                  if (lang === Language.PYTHON) {
                    const deps = `braintrust autoevals ${codeExample?.extraDependencies ?? ""}`;
                    if (pythonInstaller === PythonInstaller.UV) {
                      return `uv pip install ${deps}`;
                    }
                    return `pip install ${deps}`;
                  } else {
                    const deps = `braintrust autoevals ${codeExample?.extraDependencies ?? ""}`;
                    if (jsPackageManager === JsPackageManager.NPM) {
                      return `npm install ${deps}`;
                    } else if (jsPackageManager === JsPackageManager.PNPM) {
                      return `pnpm add ${deps}`;
                    } else {
                      return `yarn add ${deps}`;
                    }
                  }
                })()}
                language="bash"
              />
            </div>

            <div>
              <div className="mb-2 flex items-end gap-3">
                <p className="flex h-6 flex-1 items-end text-sm">
                  Add environment variables
                </p>
                <div className="h-6"></div>
              </div>
              <APIKeyOrCommand creatingApiKey={creatingApiKey}>
                <CodeToCopy
                  inline
                  highlighterClassName="bg-background"
                  data={
                    selectedProviderConfig?.environmentVariables
                      ? Object.entries(
                          selectedProviderConfig.environmentVariables,
                        )
                          .map(([key, value]) => `${key}=${value}`)
                          .join("\n")
                      : ""
                  }
                  language="yaml"
                />
              </APIKeyOrCommand>
            </div>

            <div className="flex flex-col">
              <div className="mb-2 flex items-end gap-3">
                <div className="flex h-6 w-full flex-row items-center justify-between">
                  <div className="text-sm">Trace your LLM calls</div>
                  {selectedProviderConfig?.docsUrl && (
                    <Link
                      href={selectedProviderConfig.docsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={cn(
                        buttonVariants({
                          variant: "ghost",
                          size: "xs",
                        }),
                        "text-primary-400",
                      )}
                    >
                      Learn more <ArrowUpRight className="size-3" />
                    </Link>
                  )}
                </div>
                <div className="h-6 empty:hidden">
                  {llmProvider === SdkIntegration.VERCEL && (
                    <Tabs value={framework}>
                      <TabsList className="h-6">
                        {Object.values(VercelFramework).map((framework) => (
                          <TabsTrigger
                            key={framework}
                            value={framework}
                            className="h-5 text-xs"
                            onClick={() => setFramework(framework)}
                          >
                            {framework}.js
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </Tabs>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-2">
                {providerCodeExamples.map((example: string, index: number) => (
                  <CodeToCopy
                    key={index}
                    highlighterClassName="bg-background"
                    language={
                      lang === Language.PYTHON ? "python" : "javascript"
                    }
                    data={example}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="z-10 flex justify-between gap-2 pb-8">
        <Link
          prefetch
          href={`/app/setup/${orgName}`}
          className={cn(
            buttonVariants({ variant: "ghost", size: "sm" }),
            "transition-all text-primary-500",
          )}
        >
          Back
        </Link>
        <Button
          variant="ghost"
          size="sm"
          className="transition-all text-primary-500"
          disabled={isSubmitting}
          isLoading={isSubmitting}
          onClick={() => {
            handlePlaygroundCreate();
          }}
        >
          Skip for now
        </Button>
      </div>
    </div>
  );
};

function APIKeyOrCommand({
  creatingApiKey,
  children,
}: {
  creatingApiKey: boolean;
  children: React.ReactNode;
}) {
  if (creatingApiKey) {
    return (
      <div className="flex items-center gap-2">
        <Spinner className="size-4" />
        <span className="text-sm text-primary-600">Generating API key...</span>
      </div>
    );
  }

  return <>{children}</>;
}
