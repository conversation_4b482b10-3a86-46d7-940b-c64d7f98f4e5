import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import SessionRoot from "#/ui/root";
import { SetupTracing } from "./setup-tracing";

export interface Params {
  org: string;
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  return (
    <SessionRoot loginRequired>
      <SetupTracing orgName={org_name} />
    </SessionRoot>
  );
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Setup observability",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/setup/${params.org}/trace`,
    ogTemplate: "product",
  });
}
