"use client";

import { useCallback, useEffect, useMemo, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { getProjectLogsLink } from "#/app/app/[org]/p/[project]/logs/getProjectLogsLink";
import { useAnalytics } from "#/ui/use-analytics";

import { useUser } from "#/utils/user";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { RealtimeChannel } from "#/utils/realtime-data";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { z } from "zod";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useSessionToken } from "#/utils/auth/session-token";
import { useAuth } from "@clerk/nextjs";

import { AnimatePresence, motion } from "motion/react";
import { starterProjectName } from "./trace/trace-config";
import { AccessFailed } from "#/ui/access-failed";
import Header from "#/ui/layout/header";

const orgProjectMetadataPayloadSchema = z.record(z.record(z.any()));

export default function ProjectOnboardingPage({
  children,
}: {
  children: React.ReactNode;
}) {
  const { orgs } = useUser();
  const params = useParams<{ org: string }>();
  const orgName = params?.org ?? "";
  const org = orgs[orgName];
  const { getToken } = useAuth();
  const { analytics } = useAnalytics();
  const { user } = useUser();

  const { getOrRefreshToken } = useSessionToken();
  useEffect(() => {
    analytics?.track("onboarding_page_viewed", {
      orgName: org?.name,
    });
  }, [analytics, org?.name]);

  const router = useRouter();
  const finishedOnboarding = useRef<boolean>(false);

  const processNewLog = useCallback(
    async (projectId: string) => {
      if (finishedOnboarding.current) return;
      try {
        const freshProjects = await invokeServerAction<
          typeof getProjectSummary
        >({
          fName: "getProjectSummary",
          args: { org_name: org.name },
          getToken,
        });

        const project = freshProjects?.find((p) => p.project_id === projectId);
        if (project) {
          // Delete start project if unused for tutorial
          if (project.project_name !== starterProjectName) {
            const starterProject = freshProjects?.find(
              (p) => p.project_name === starterProjectName,
            );
            if (starterProject) {
              try {
                await fetch(`/api/project/delete_id`, {
                  method: "POST",
                  headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                    id: starterProject.project_id,
                  }),
                });
              } catch (error) {
                console.warn("Error deleting starter project:", error);
              }
            }
          }

          router.push(
            getProjectLogsLink({
              orgName: org?.name,
              projectName: project.project_name,
            }),
          );
          finishedOnboarding.current = true;
        } else {
          console.warn("Project not found");
        }
      } catch (e) {
        toast.error("Failed to query tutorial logs");
      }
    },
    [org?.name, router, getToken],
  );

  const processOrgProjectMetadataEvent = useCallback(
    async (eventRaw: unknown) => {
      if (finishedOnboarding.current) return;
      const event = orgProjectMetadataPayloadSchema.parse(eventRaw);
      const projectLogData = event["project_log"];
      if (projectLogData) {
        const projectIds = Object.keys(projectLogData);
        if (projectIds.length > 0) {
          for (const projectId of projectIds) {
            await processNewLog(projectId);
            return;
          }
        }
      }
    },
    [processNewLog],
  );

  // Subscribe to updates to this org. Trigger processOrgProjectMetadataEvent on
  // each event.
  const channelStatus = useRef<"empty" | "creating" | "created">("empty");
  const fetchChannelUrl = useMemo(
    () =>
      org.api_url && org.id
        ? `${org.api_url}/broadcast-key?` +
          new URLSearchParams({
            object_type: "org_project_metadata",
            id: org.id,
            audit_log: "0",
          })
        : undefined,
    [org.api_url, org.id],
  );
  useEffect(() => {
    const fetchChannel = async () => {
      if (
        !(
          fetchChannelUrl &&
          org.api_url &&
          user &&
          channelStatus.current === "empty"
        )
      ) {
        return;
      }
      channelStatus.current = "creating";
      try {
        const channelName = await (
          await apiFetchGet(fetchChannelUrl, await getOrRefreshToken())
        ).json();
        if (!(channelName.channel && channelName.token)) {
          throw new Error(
            `Invalid channel info: ${JSON.stringify(channelName)}`,
          );
        }
        new RealtimeChannel(
          channelName.channel,
          channelName.token,
          {
            user_id: user.id,
            email: user.email,
            avatar_url: user.avatar_url,
          },
          org,
          () => true,
          (payload) => {
            processOrgProjectMetadataEvent(payload);
          },
        );
        channelStatus.current = "created";
      } catch (e) {
        console.error("Failed to create channel\n", e);
        channelStatus.current = "empty";
      }
    };
    fetchChannel();
  }, [
    fetchChannelUrl,
    user,
    org,
    processOrgProjectMetadataEvent,
    getOrRefreshToken,
  ]);

  if (!org) {
    return (
      <div className="h-screen w-screen items-center justify-center">
        <Header noCTA />
        <AccessFailed objectType="organization" objectName={orgName} />
      </div>
    );
  }

  return (
    <div className="flex h-screen w-screen overflow-y-auto bg-primary-50">
      <div className="relative mx-auto flex size-full max-w-screen-sm px-4">
        <motion.div className="relative flex size-full pt-40">
          <AnimatePresence>{children}</AnimatePresence>
        </motion.div>
      </div>
    </div>
  );
}
