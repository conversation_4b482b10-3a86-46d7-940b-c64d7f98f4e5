"use client";

import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ListTree, MousePointerClick } from "lucide-react";
import Link from "next/link";
import { motion } from "motion/react";

export const GetStarted = ({ orgName }: { orgName: string }) => {
  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-6 flex w-full flex-col justify-center">
        <h1 className="mb-2 font-display text-3xl font-semibold">
          Let&apos;s get started
        </h1>
        <p className="text-base text-primary-600">
          Braintrust is the end-to-end platform for building world-class AI
          apps. Choose an option below to continue.
        </p>
      </div>
      <div className="flex flex-col gap-3 md:flex-row">
        <motion.div
          className="w-full flex-1"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ y: 5 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          <Link
            prefetch
            className={cn(
              buttonVariants({ variant: "border" }),
              "flex flex-col gap-0 w-full flex-1 h-fit bg-primary-100 border overflow-hidden px-5 py-4 hover:border-primary-300 hover:bg-primary-200 group bg-center justify-start pr-0 rounded-xl text-primary-900",
            )}
            href={`/app/setup/${orgName}/trace`}
          >
            <div className="z-10 flex w-full flex-col gap-4 text-pretty">
              <div className="flex flex-col gap-2 text-start">
                <ListTree className="mb-4 size-7 text-primary-500" />
                <div className="font-display text-2xl font-semibold leading-tight">
                  Trace an existing app
                </div>
                <span className="text-sm font-normal text-primary-600">
                  I have an existing AI app in production and I want to add LLM
                  observability
                </span>
              </div>
            </div>
          </Link>
        </motion.div>
        <motion.div
          className="w-full flex-1"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ y: 5 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.1 }}
        >
          <Link
            prefetch
            className={cn(
              buttonVariants({ variant: "border" }),
              "flex flex-col gap-0 w-full flex-1 h-fit bg-primary-100 border overflow-hidden px-5 py-4 hover:border-primary-300 hover:bg-primary-200 group bg-center justify-start pr-0 rounded-xl text-primary-900",
            )}
            href={`/app/setup/${orgName}/providers`}
          >
            <div className="z-10 flex w-full flex-col items-start gap-4 text-pretty">
              <div className="flex flex-col gap-2 text-start">
                <MousePointerClick className="mb-4 size-7 text-primary-500" />
                <div className="font-display text-2xl font-semibold leading-tight">
                  Explore new AI ideas
                </div>
                <span className="text-sm font-normal text-primary-600">
                  I&apos;m exploring ideas, prompts, and evals with Braintrust
                </span>
              </div>
            </div>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};
