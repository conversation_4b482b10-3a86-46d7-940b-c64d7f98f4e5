import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import { ClientPage } from "./clientpage";
import SessionRoot from "#/ui/root";

export interface Params {
  org: string;
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  return (
    <SessionRoot loginRequired>
      <ClientPage orgName={org_name} />
    </SessionRoot>
  );
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Get started",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/setup/${params.org}`,
    ogTemplate: "product",
  });
}
