import { type LoadedBtSessionToken } from "#/utils/auth/session-token";
import { apiFetchGet, apiPost } from "#/utils/btapi/fetch";
import { _urljoin } from "@braintrust/core";
import { type CronJobState } from "@braintrust/local/api-schema";

export async function registerCronWithServiceToken({
  automationId,
  apiUrl,
  sessionToken,
  cronJob,
  serviceToken,
}: {
  automationId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  cronJob: CronJobState;
  serviceToken: string;
}) {
  await apiPost({
    url: _urljoin(apiUrl, "automation/cron"),
    sessionToken,
    payload: {
      automation_id: automationId,
      cron: cronJob,
      service_token: serviceToken,
    },
    alreadySerialized: false,
  });
}

export async function runCronJob({
  automationId,
  apiUrl,
  sessionToken,
  iterations,
}: {
  automationId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  iterations?: number;
}): Promise<unknown> {
  const resp = await apiPost({
    url: _urljoin(apiUrl, "automation/cron", automationId, "run"),
    sessionToken,
    payload: {
      iterations: iterations ?? 1,
    },
    alreadySerialized: false,
  });
  return resp.json();
}

export async function resetCronJob({
  automationId,
  apiUrl,
  sessionToken,
}: {
  automationId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<unknown> {
  const resp = await apiPost({
    url: _urljoin(apiUrl, "automation/cron", automationId, "reset"),
    sessionToken,
    payload: {},
    alreadySerialized: false,
  });
  return resp.json();
}

export async function fetchCronJobStatus({
  automationId,
  apiUrl,
  sessionToken,
}: {
  automationId: string;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}): Promise<unknown> {
  const resp = await apiFetchGet(
    _urljoin(apiUrl, "automation/cron", automationId, "status"),
    sessionToken,
  );
  return resp.json();
}
