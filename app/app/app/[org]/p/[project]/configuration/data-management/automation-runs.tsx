import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { type ProjectAutomation } from "@braintrust/core/typespecs";
import {
  type CronJobStatusResponse,
  cronJobStatusResponseSchema,
  runCronJobResponseSchema,
} from "@braintrust/local/api-schema";
import {
  type PropsWithChildren,
  type ReactNode,
  useCallback,
  useEffect,
  useState,
} from "react";
import { fetchCronJobStatus, resetCronJob, runCronJob } from "./api-requests";
import { DialogFooter } from "#/ui/dialog";
import { Play, RefreshCw } from "lucide-react";
import { Button } from "#/ui/button";
import prettyBytes from "pretty-bytes";
import { relativeTimeMs, smartTimeFormat } from "#/ui/date";
import { ErrorBanner } from "#/ui/error-banner";
import { pluralize } from "#/utils/plurals";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";

export const AutomationRuns = ({
  automation,
}: {
  automation: ProjectAutomation | null;
}) => {
  const automationId = automation?.id;
  const [jobStatus, setJobStatus] = useState<CronJobStatusResponse | null>(
    null,
  );
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const handleRefresh = useCallback(async () => {
    if (!automationId) return;

    setIsRefreshing(true);
    setError(null);

    try {
      const response = await fetchCronJobStatus({
        automationId,
        apiUrl: org.api_url,
        sessionToken: await getOrRefreshToken(),
      });

      const parsed = cronJobStatusResponseSchema.safeParse(response);
      if (!parsed.success) {
        setError(parsed.error.message);
        return;
      }
      setJobStatus(parsed.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to refresh status");
    } finally {
      setIsRefreshing(false);
    }
  }, [automationId, getOrRefreshToken, org.api_url]);

  useEffect(() => {
    handleRefresh();
  }, [handleRefresh]);

  const handleRun = useCallback(async () => {
    if (!automationId) return;

    setIsRunning(true);
    setError(null);

    try {
      const response = await runCronJob({
        automationId,
        apiUrl: org.api_url,
        sessionToken: await getOrRefreshToken(),
      });

      const parsed = runCronJobResponseSchema.safeParse(response);
      if (!parsed.success) {
        setError(parsed.error.message);
        return;
      }
      if (parsed.data.result?.type === "btql_export") {
        setJobStatus((prev) => ({
          last_executed: prev?.last_executed || null,
          next_execution: prev?.next_execution || null,
          state: parsed.data.result!,
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to run job");
    } finally {
      setIsRunning(false);
    }
  }, [automationId, getOrRefreshToken, org.api_url]);

  const [confirmReset, setConfirmReset] = useState(false);

  const handleReset = useCallback(async () => {
    if (!automationId) return;

    setIsResetting(true);
    setError(null);

    try {
      await resetCronJob({
        automationId,
        apiUrl: org.api_url,
        sessionToken: await getOrRefreshToken(),
      });

      // After reset, refresh the status to get the updated state
      await handleRefresh();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to reset automation",
      );
    } finally {
      setIsResetting(false);
    }
  }, [automationId, getOrRefreshToken, org.api_url, handleRefresh]);

  if (!automation) {
    return null;
  }

  const jobState = jobStatus?.state;
  const currentExecution = jobState?.last_execution;
  const hasError = currentExecution?.error;

  return (
    <>
      <div className="pt-6">
        {error && <ErrorBanner skipErrorReporting>{error}</ErrorBanner>}

        {jobState && (
          <>
            <Property label="Type">
              {jobState.type === "btql_export" ? "BTQL export" : jobState.type}
            </Property>
            {jobState.cursor && (
              <Property label="Cursor">
                <span className="font-mono font-normal">{jobState.cursor}</span>
              </Property>
            )}
          </>
        )}

        {jobState?.all_executions && (
          <Property label="All runs">
            <div className="flex w-full flex-wrap gap-2">
              <Metric label="Total rows processed">
                {formatNumber(jobState.all_executions.rows)}
              </Metric>
              <Metric label="Total data processed">
                {prettyBytes(jobState.all_executions.bytes)}
              </Metric>
              {jobState.all_executions.duration_ms > 0 && (
                <Metric label="Total duration">
                  {relativeTimeMs(jobState.all_executions.duration_ms)}
                </Metric>
              )}
            </div>
          </Property>
        )}

        {jobStatus?.last_executed && (
          <Property label="Last run">
            <div className="mb-2">
              {smartTimeFormat(new Date(jobStatus.last_executed).getTime())}
            </div>
            {currentExecution && (
              <>
                {hasError && (
                  <ErrorBanner className="mt-0" skipErrorReporting>
                    {currentExecution.error}
                  </ErrorBanner>
                )}
                <div className="flex w-full flex-wrap gap-2">
                  <Metric label="Rows processed">
                    {formatNumber(currentExecution.rows)}
                  </Metric>
                  <Metric label="Data size">
                    {prettyBytes(currentExecution.bytes)}
                  </Metric>
                  <Metric label="Duration">
                    {relativeTimeMs(currentExecution.duration_ms)}
                  </Metric>
                  {currentExecution.files.length > 0 && (
                    <Metric
                      label={pluralize(
                        currentExecution.files.length,
                        "Generated file",
                      )}
                    >
                      {currentExecution.files.length}
                    </Metric>
                  )}
                </div>
              </>
            )}
          </Property>
        )}
      </div>

      <DialogFooter>
        <div className="flex flex-1">
          <Button
            size="sm"
            onClick={() => setConfirmReset(true)}
            disabled={isResetting || !automationId}
            isLoading={isResetting}
            className="text-bad-600"
          >
            Reset automation
          </Button>
        </div>
        <Button
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing || !automationId}
          IconLeft={RefreshCw}
          isLoading={isRefreshing}
        >
          Refresh
        </Button>
        <Button
          size="sm"
          onClick={handleRun}
          disabled={isRunning || !automationId}
          IconLeft={Play}
          isLoading={isRunning}
        >
          Run once
        </Button>
      </DialogFooter>

      <ConfirmationDialog
        open={confirmReset}
        onOpenChange={setConfirmReset}
        onConfirm={handleReset}
        confirmText="Reset"
        title="Are you sure you want to fully reset this automation?"
        description="All automation state will be reset and data will be re-exported."
      />
    </>
  );
};

const Property = ({
  label,
  children,
}: PropsWithChildren<{ label: ReactNode }>) => (
  <div className="mb-5 flex gap-2 text-sm text-primary-500">
    <span className="w-36">{label}</span>
    <span className="flex-1 font-medium text-primary-800">{children}</span>
  </div>
);

const Metric = ({
  label,
  children,
}: PropsWithChildren<{ label: ReactNode }>) => (
  <div className="mb-2 flex flex-1 flex-col rounded-md p-2 bg-primary-100">
    <span className="text-base font-semibold text-primary-800">{children}</span>
    <span className="text-xs font-normal text-primary-500">{label}</span>
  </div>
);
