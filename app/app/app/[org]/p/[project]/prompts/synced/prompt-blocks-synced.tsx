"use client";

import { type TransactionId } from "#/utils/duckdb";
import { memo, useCallback, useMemo } from "react";
import { type ModelDetails } from "#/ui/prompts/models";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import StyledDndContext from "#/ui/styled-dnd-context";
import { DragOverlay } from "@dnd-kit/core";
import {
  horizontalListSortingStrategy,
  SortableContext,
} from "@dnd-kit/sortable";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { usePlaygroundFullscreenTaskIndex } from "#/ui/query-parameters";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import { PlaygroundPromptCardSynced } from "./playground-prompt-card-synced";
import { useDraggablePromptsSynced } from "./use-draggable-prompts-synced";
import { useAtomValue } from "jotai";
import { selectAtom } from "jotai/utils";
import isEqual from "lodash.isequal";
import { useSyncedPrompts } from "./use-synced-prompts";
import { type ModelSpec } from "@braintrust/proxy/schema";
import { type PlaygroundRecord } from "../../playgrounds/[playground]/playx/playx";
import {
  type PromptExtensionsParams,
  type JSONStructure,
} from "#/ui/prompts/hooks";
import { toast } from "sonner";
import { NoAISecrets } from "#/ui/prompts/empty";

interface PromptBlocksProps {
  isReadOnly?: boolean;
  orgName: string;
  projectId?: string;
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
  deletePrompt(id: string): Promise<TransactionId | null>;
  runPrompts: (args: {
    rowIdx?: number;
    datasetRecordId?: string;
    inputValue?: Partial<PlaygroundRecord>;
  }) => void;
  allAvailableModels: Record<string, ModelSpec>;
  copilotContext?: PlaygroundCopilotContext;
  addPromptButton?: React.ReactNode;
  diffEnabled?: boolean;
  datasetId?: string;
  datasetJsonStructure: JSONStructure;
  promptExtensionsParams: PromptExtensionsParams;
  rowData?: Record<string, unknown>;
  extraMessagesPath?: string | null;
  showNoConfiguredSecretsMessage: boolean;
}

export const PromptBlocksSynced = memo(
  ({
    isReadOnly,
    orgName,
    projectId,
    modelOptionsByProvider,
    deletePrompt,
    savedPromptMeta,
    runPrompts,
    allAvailableModels,
    copilotContext,
    addPromptButton,
    diffEnabled,
    datasetId,
    datasetJsonStructure,
    promptExtensionsParams,
    rowData,
    extraMessagesPath,
    showNoConfiguredSecretsMessage,
  }: PromptBlocksProps) => {
    const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
    // We only need the ids to render prompt cards,
    // so we can avoid extra re-renders here by using a selector with an equality function.
    const blocks = useAtomValue(
      useMemo(
        () =>
          selectAtom(
            sortedSyncedPromptsAtom_ROOT,
            (prompts) => prompts.map((prompt) => prompt.id),
            isEqual,
          ),
        [sortedSyncedPromptsAtom_ROOT],
      ),
    );

    const [fullscreenTaskIndex, setFullscreenTaskIndex] =
      usePlaygroundFullscreenTaskIndex();

    const onRunPrompts = useCallback(() => {
      if (showNoConfiguredSecretsMessage) {
        toast("No configured secrets", {
          description: <NoAISecrets orgName={orgName} />,
        });
        return;
      }
      runPrompts({});
    }, [orgName, runPrompts, showNoConfiguredSecretsMessage]);

    const { draggingPrompt, handleDragStart, handleDragEnd } =
      useDraggablePromptsSynced(savedPromptMeta);

    return (
      <StyledDndContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
        {blocks.length === 0 && (
          <TableEmptyState
            className="flex-1 justify-center"
            label="There are no tasks in this playground yet"
          >
            {addPromptButton}
          </TableEmptyState>
        )}
        <SortableContext
          items={blocks}
          strategy={horizontalListSortingStrategy}
        >
          {blocks.map((id, i) => {
            if (fullscreenTaskIndex !== null && fullscreenTaskIndex !== i) {
              return null;
            }

            return (
              <PlaygroundPromptCardSynced
                showNoConfiguredSecretsMessage={showNoConfiguredSecretsMessage}
                isSortable
                isReadOnly={isReadOnly}
                isFullscreen={fullscreenTaskIndex === i}
                setFullscreen={setFullscreenTaskIndex}
                key={id}
                index={i}
                deletePrompt={deletePrompt}
                datasetId={datasetId}
                savedPromptMeta={savedPromptMeta}
                orgName={orgName}
                projectId={projectId}
                allAvailableModels={allAvailableModels}
                modelOptionsByProvider={modelOptionsByProvider}
                promptId={id}
                diffId={diffEnabled && i !== 0 ? blocks[0] : undefined}
                onRunPrompts={onRunPrompts}
                copilotContext={copilotContext}
                datasetJsonStructure={datasetJsonStructure}
                promptExtensionsParams={promptExtensionsParams}
                rowData={rowData}
                extraMessagesPath={extraMessagesPath}
              />
            );
          })}
        </SortableContext>
        <DragOverlay>
          {draggingPrompt && (
            <div className="cursor-grabbing truncate rounded-md border-2 p-3 text-sm font-medium shadow-md bg-background border-accent-500 text-primary-700">
              {draggingPrompt.displayName}
            </div>
          )}
        </DragOverlay>
      </StyledDndContext>
    );
  },
);
PromptBlocksSynced.displayName = "PromptBlocksSynced";
