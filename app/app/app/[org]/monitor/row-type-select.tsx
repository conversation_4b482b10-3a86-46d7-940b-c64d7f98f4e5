import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";

import { type MonitorViewOptions } from "@braintrust/core/typespecs";

type RowType = NonNullable<MonitorViewOptions["type"]>;

export function RowTypeSelect({
  value,
  onChange,
}: {
  value: RowType;
  onChange: (rowType: RowType) => void;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown>
          {value === "project" ? "Logs" : "Experiments"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem onSelect={() => onChange("project")}>
          Logs
        </DropdownMenuItem>
        <DropdownMenuItem onSelect={() => onChange("experiment")}>
          Experiments
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
