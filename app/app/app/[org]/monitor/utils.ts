import {
  type ViewData,
  type ViewOptions,
  type MonitorViewOptions,
} from "@braintrust/core/typespecs";
import { type DBMonitorView, type View } from "#/utils/view/use-view-generic";

export function isMonitorViewOptions(
  options: ViewOptions | null,
): options is { viewType: "monitor"; options: MonitorViewOptions } {
  if (!options) {
    return false;
  }
  return "viewType" in options && options.viewType === "monitor";
}

export function getMetricsLabel(metric: string) {
  switch (metric) {
    case "p50_duration":
    case "p50_time_to_first_token":
      return "P50";
    case "p95_duration":
    case "p95_time_to_first_token":
      return "P95";
    case "sum_tokens":
    case "totalCost":
      return "Total";
    case "sum_prompt_uncached_tokens":
    case "promptUncachedTokensCost":
      return "Prompt (uncached)";
    case "sum_completion_tokens":
    case "completionTokensCost":
      return "Completion";
    case "sum_prompt_cached_tokens":
    case "promptCachedTokensCost":
      return "Prompt (cache read)";
    case "sum_prompt_cache_creation_tokens":
    case "promptCacheCreationTokensCost":
      return "Prompt (cache write)";
    case "count":
      return "Traces";
    case "spans":
      return "Spans";
    case "llm_count":
      return "LLM calls";
    case "tool_count":
      return "Tool calls";
    default:
      return metric;
  }
}

export function createViewSaveHandler({
  selectedView,
  updateView,
  currentViewData,
  currentViewOptions,
}: {
  selectedView: View<DBMonitorView> | null;
  updateView: (params: {
    viewId: string;
    viewData?: ViewData;
    options?: ViewOptions;
  }) => Promise<unknown>;
  currentViewData: ViewData;
  currentViewOptions: MonitorViewOptions;
}) {
  return async (overrides?: {
    options?: Partial<MonitorViewOptions>;
    viewData?: Partial<ViewData>;
  }) => {
    // Don't save if no view is selected or if default view is selected
    if (!selectedView || selectedView.builtin || !selectedView.id) {
      return;
    }

    const optionsToSave = overrides?.options
      ? {
          ...currentViewOptions,
          ...overrides.options,
        }
      : currentViewOptions;

    const viewDataToSave = overrides?.viewData
      ? {
          ...currentViewData,
          ...overrides.viewData,
        }
      : currentViewData;

    await updateView({
      viewId: selectedView.id,
      viewData: viewDataToSave,
      options: {
        viewType: "monitor",
        options: optionsToSave,
      },
    });
  };
}
