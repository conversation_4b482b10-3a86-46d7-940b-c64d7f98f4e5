import { z } from "zod";

const upgrade_modal_opened = z.object({ orgName: z.string() });
const signup = z.object({
  source: z.enum([
    "in_app",
    "cta_button",
    "logged_out_playground",
    "clerk_signup",
  ]),
});
const signin = z.object({
  source: z.enum(["in_app", "cta_button", "logged_out_playground"]),
});

const projectCreateEntryPoints = [
  "projectsSidebarDropdown",
  "projectsHomePageNewProjectButton",
  "projectsHomePageEmptyState",
  "onboardingFlow",
] as const;

export type ProjectCreateEntryPoint = (typeof projectCreateEntryPoints)[number];

const projectCreateAttempt = z.object({
  projectName: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreateAbandon = z.object({
  projectName: z.string(),
  reason: z.enum(["error", "closed"]),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreate = z.object({
  projectName: z.string(),
  projectId: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const downgradeProSubscription = z.object({
  reason: z.string(),
  otherReason: z.string().optional(),
});

// Server-side events
const playgroundRun = z.object({
  projectId: z.string().nullable(),
  playgroundId: z.string(),
  outcome: z.enum(["success", "error", "cancelled"]),
  errorMessage: z.string().optional(),
  numPrompts: z.number().optional(),
  numDatasetRows: z.number().optional(),
  maxConcurrency: z.number().optional(),
  strict: z.boolean().optional(),
  hasDataset: z.boolean().optional(),
  durationMs: z.number().optional(),
  // Run config metadata
  model: z.string().optional(),
  provider: z.string().optional(),
  temperature: z.number().optional(),
  topP: z.number().optional(),
  maxTokens: z.number().optional(),
  streamingEnabled: z.boolean().optional(),
  // Data context
  datasetId: z.string().optional(),
  // Scorer information
  numScorers: z.number().optional(),
  scorers: z
    .array(
      z.object({
        id: z.string().optional(),
        name: z.string().optional(),
        type: z.enum(["global", "function"]).optional(),
        isCustom: z.boolean().optional(),
      }),
    )
    .optional(),
  // Performance
  latencyBreakdown: z
    .object({
      queueMs: z.number().optional(),
      execMs: z.number().optional(),
      postMs: z.number().optional(),
    })
    .optional(),
  // Comparison tasks summary
  comparisonTasksCount: z.number().optional(),
  comparisonTasks: z
    .array(
      z.object({
        index: z.number().optional(),
        name: z.string().optional(),
        model: z.string().optional(),
        provider: z.string().optional(),
        customEndpoint: z.string().nullable().optional(),
        temperature: z.number().optional(),
        topP: z.number().optional(),
        maxTokens: z.number().optional(),
        frequencyPenalty: z.number().optional(),
        presencePenalty: z.number().optional(),
        streamingEnabled: z.boolean().optional(),
        promptType: z.enum(["chat", "completion"]).optional(),
      }),
    )
    .optional(),
  // Traceability
  runId: z.string().optional(),
  requestId: z.string().optional(),
  experimentId: z.string().optional(),
  promptVersionId: z.string().optional(),
  // UX context
  entryPoint: z.string().optional(),
  // Common server-side fields
  orgId: z.string().optional(),
  source: z.literal("server"),
  timestamp: z.string().optional(),
});

export const eventSchemas = {
  upgrade_modal_opened,
  signup,
  signin,
  projectCreateAttempt,
  projectCreate,
  projectCreateAbandon,
  downgradeProSubscription,
  playgroundRun,
} as const;

type EventSchemas = typeof eventSchemas;
export type EventName = keyof EventSchemas;
export type EventProps<K extends EventName> = z.infer<EventSchemas[K]>;
