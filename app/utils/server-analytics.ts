import "server-only";

import { z } from "zod";
import { trackSegmentEvent } from "#/utils/segment/segment";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import type { AuthLookup } from "#/utils/server-util";

// Base schema for all server-side events
const baseServerEventSchema = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  timestamp: z.string().optional(), // ISO string
  source: z.literal("server"),
  // Add any other common fields here
});

// Playground run event schema
export const playgroundRunEventSchema = baseServerEventSchema.extend({
  event: z.literal("playgroundRun"),
  properties: z.object({
    projectId: z.string().nullable(),
    playgroundId: z.string(),
    outcome: z.enum(["success", "error", "cancelled"]),
    errorMessage: z.string().optional(),
    numPrompts: z.number().optional(),
    numDatasetRows: z.number().optional(),
    maxConcurrency: z.number().optional(),
    strict: z.boolean().optional(),
    hasDataset: z.boolean().optional(),
    durationMs: z.number().optional(),
    // Run config metadata
    model: z.string().optional(),
    provider: z.string().optional(),
    temperature: z.number().optional(),
    topP: z.number().optional(),
    maxTokens: z.number().optional(),
    streamingEnabled: z.boolean().optional(),
    // Data context
    datasetId: z.string().optional(),
    // Scorer information
    numScorers: z.number().optional(),
    scorers: z
      .array(
        z.object({
          id: z.string().optional(),
          name: z.string().optional(),
          type: z.enum(["global", "function"]).optional(),
          isCustom: z.boolean().optional(),
        }),
      )
      .optional(),
    // Performance
    latencyBreakdown: z
      .object({
        queueMs: z.number().optional(),
        execMs: z.number().optional(),
        postMs: z.number().optional(),
      })
      .optional(),
    // Comparison tasks summary
    comparisonTasksCount: z.number().optional(),
    comparisonTasks: z
      .array(
        z.object({
          index: z.number().optional(),
          name: z.string().optional(),
          model: z.string().optional(),
          provider: z.string().optional(),
          customEndpoint: z.string().nullable().optional(),
          temperature: z.number().optional(),
          topP: z.number().optional(),
          maxTokens: z.number().optional(),
          frequencyPenalty: z.number().optional(),
          presencePenalty: z.number().optional(),
          streamingEnabled: z.boolean().optional(),
          promptType: z.enum(["chat", "completion"]).optional(),
        }),
      )
      .optional(),
    // Traceability
    runId: z.string().optional(),
    requestId: z.string().optional(),
    experimentId: z.string().optional(),
    promptVersionId: z.string().optional(),
    // UX context
    entryPoint: z.string().optional(),
  }),
});

// Add more event schemas here as needed
export const serverEventSchemas = {
  playgroundRun: playgroundRunEventSchema,
} as const;

type ServerEventSchemas = typeof serverEventSchemas;
export type ServerEventName = keyof ServerEventSchemas;
export type ServerEventProps<K extends ServerEventName> = z.infer<
  ServerEventSchemas[K]
>;
export type ServerEventProperties<K extends ServerEventName> = z.infer<
  ServerEventSchemas[K]
>["properties"];

/**
 * Server-side analytics logging utility
 * Provides a type-safe way to log server-side events with proper validation
 */
export class ServerAnalytics {
  private authLookup?: AuthLookup;

  constructor(authLookup?: AuthLookup) {
    this.authLookup = authLookup;
  }

  /**
   * Log a server-side event with proper validation and error handling
   */
  async track<K extends ServerEventName>(
    eventName: K,
    properties: ServerEventProperties<K>,
    options?: {
      authLookup?: AuthLookup;
      userId?: string;
      orgId?: string;
    },
  ): Promise<void> {
    try {
      // Get auth info if not provided
      const authLookup =
        options?.authLookup ??
        this.authLookup ??
        (await getServerSessionAuthLookup());

      if (!authLookup?.user_id) {
        console.warn(
          "ServerAnalytics: No user ID available for event tracking",
          { eventName },
        );
        return;
      }

      // Construct the full event object
      const eventData = {
        userId: options?.userId ?? authLookup.user_id,
        orgId: options?.orgId ?? authLookup.org_id ?? undefined,
        timestamp: new Date().toISOString(),
        source: "server" as const,
        event: eventName,
        properties,
      } satisfies ServerEventProps<K>;

      // Validate the event data
      const schema = serverEventSchemas[eventName];
      const validationResult = schema.safeParse(eventData);

      if (!validationResult.success) {
        console.error("ServerAnalytics: Invalid event data", {
          eventName,
          errors: validationResult.error.errors,
          data: eventData,
        });
        return;
      }

      // Track the event via Segment
      await trackSegmentEvent({
        userId: eventData.userId,
        event: eventName,
        properties: {
          ...eventData.properties,
          orgId: eventData.orgId,
          source: eventData.source,
          timestamp: eventData.timestamp,
        },
      });
    } catch (error) {
      // Don't let analytics errors break the main functionality
      console.error("ServerAnalytics: Failed to track event", {
        eventName,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
}

/**
 * Create a ServerAnalytics instance with the current auth context
 */
export async function createServerAnalytics(
  authLookup?: AuthLookup,
): Promise<ServerAnalytics> {
  const resolvedAuthLookup = authLookup ?? (await getServerSessionAuthLookup());
  return new ServerAnalytics(resolvedAuthLookup);
}

/**
 * Convenience function for one-off event tracking
 */
export async function trackServerEvent<K extends ServerEventName>(
  eventName: K,
  properties: ServerEventProperties<K>,
  options?: {
    authLookup?: AuthLookup;
    userId?: string;
    orgId?: string;
  },
): Promise<void> {
  const analytics = await createServerAnalytics(options?.authLookup);
  return analytics.track(eventName, properties, options);
}
