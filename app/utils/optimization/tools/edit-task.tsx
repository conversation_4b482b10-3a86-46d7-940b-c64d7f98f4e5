import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "#/ui/dialog";
import { <PERSON><PERSON> } from "#/ui/button";
import { PromptDiff } from "#/app/app/[org]/p/[project]/prompts/prompt-diff";
import { type TaskFunctionDefinition } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/clientpage";
import {
  type PromptData,
  type RuntimeContext,
} from "@braintrust/core/typespecs";
import { promptSchema, type UIFunctionData } from "#/ui/prompts/schema";

export interface InlineTaskDefinition {
  id: string;
  inline_prompt?: PromptData;
  inline_function?: UIFunctionData;
  inline_context?: RuntimeContext;
  code?: string;
}

export function parseInlineTaskDefinition(
  definition: TaskFunctionDefinition,
): InlineTaskDefinition {
  if ("inline_context" in definition && "code" in definition) {
    const inlineCodeFunction = {
      type: "code" as const,
      data: {
        type: "inline" as const,
        runtime_context: definition.inline_context,
        code: definition.code,
      },
    };

    const parsedFunction =
      promptSchema.shape.function_data.safeParse(inlineCodeFunction);
    if (!parsedFunction.success) {
      throw new Error("invalid function: " + parsedFunction.error.message);
    }
    return {
      id: definition.id,
      inline_context: definition.inline_context,
      code: definition.code,
      inline_function: parsedFunction.data,
    };
  }

  if (!("inline_prompt" in definition) || !("inline_function" in definition)) {
    throw new Error("missing inline prompt or inline function");
  } else if (!definition.inline_prompt) {
    throw new Error("missing inline prompt");
  } else if (!definition.inline_function) {
    throw new Error("missing inline function");
  }

  const parsedFunction = promptSchema.shape.function_data.safeParse(
    definition.inline_function,
  );
  if (!parsedFunction.success) {
    throw new Error("invalid function: " + parsedFunction.error.message);
  }
  return {
    id: definition.id,
    inline_prompt: definition.inline_prompt,
    inline_function: parsedFunction.data,
  };
}

export interface EditPromptDialogProps {
  index: number;
  promptData: object;
  newPromptData: object;
  onCancel: (error: string) => void;
  onSave: () => void;
}

export function EditPromptDialog({
  index,
  promptData,
  newPromptData,
  onCancel,
  onSave,
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
} & EditPromptDialogProps) {
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          onCancel("user rejected");
        }
        onOpenChange(open);
      }}
    >
      <DialogContent className="block h-[80vh] w-full sm:max-w-screen-sm">
        <DialogHeader>
          <DialogTitle>Edit task</DialogTitle>
          <DialogDescription>Edit task {index}</DialogDescription>
        </DialogHeader>
        <PromptDiff
          promptData={newPromptData}
          diffPromptData={promptData}
          rowId={index.toString()}
        />
        <DialogFooter>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              onCancel(
                "user rejected. the value would have been changed to:\n" +
                  JSON.stringify(newPromptData, null, 2),
              );
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            onClick={() => {
              onSave();
              onOpenChange(false);
            }}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
