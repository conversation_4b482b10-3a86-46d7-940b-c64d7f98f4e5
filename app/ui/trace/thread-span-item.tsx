import { deserializePlainStringAsJSON } from "braintrust";
import { cn } from "#/utils/classnames";
import { SpanName } from "./span-header";
import { ThreadSpanMetrics } from "./thread-span-metrics";
import { <PERSON><PERSON><PERSON>iewer } from "#/ui/markdown";
import { MessageBubble, type LLMMessageType } from "#/ui/LLMView";
import { type ModelCosts } from "#/ui/prompts/models";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import {
  getDiffRight,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { isEmpty } from "#/utils/object";
import { type Span } from "@braintrust/local";
import { ArrowUpRight } from "lucide-react";
import { type Virtualizer } from "@tanstack/react-virtual";

export const doesSpanHaveError = (span: Span) => {
  if (isEmpty(span?.data.error)) {
    return false;
  }

  if (isDiffObject(span?.data.error) && span?.data.error[DiffRightField]) {
    return span?.data.error[DiffRightField];
  }

  return span?.data.error;
};

export const ThreadSpanItem = ({
  span,
  spanType,
  onClickSpan,
  processSpanData,
  toolResponses,
  // allAvailableModelCosts,
  isFirst,
  virtualizer,
}: {
  span: Span;
  spanType: "llm" | "score";
  onClickSpan: (span: Span) => void;
  processSpanData: (span: Span) => {
    messages: LLMMessageType[];
    toolDefinitions: Map<string, string>;
  };
  toolResponses: Map<string, unknown>;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  isFirst: boolean;
  virtualizer: Virtualizer<HTMLDivElement, Element>;
}) => {
  const spanError = doesSpanHaveError(span);

  const head = (
    <button
      onClick={() => onClickSpan(span)}
      className="mb-3 inline-flex flex-wrap items-center gap-2.5 rounded-lg py-0.5 pl-1.5 pr-2 transition-colors bg-primary-100 hover:bg-primary-200"
    >
      <SpanName
        hasError={!!spanError}
        span={span}
        className="mb-0 items-center text-xs font-medium text-primary-600"
        iconClassName="rounded size-5 p-1 translate-y-0"
        useDarkIconStyle
      />
      <ThreadSpanMetrics span={span} />
      <ArrowUpRight className="size-3 text-primary-500" />
    </button>
  );

  if (spanType === "score") {
    const metadataString =
      typeof span.data.metadata === "string" ? span.data.metadata : "{}";
    const { value: metadata } = deserializePlainStringAsJSON(metadataString);
    const score = Object.values(span.data.scores ?? {})[0] ?? undefined;

    return (
      <div
        className={cn(
          "border-t py-4 border-primary-100",
          isFirst && "border-t-0 pt-0",
        )}
      >
        {head}
        <div className="flex flex-col gap-3 rounded-xl p-3 bg-good-50">
          {score != null ? (
            <div className="text-lg font-semibold">
              {(Number(getDiffRight(score)) * 100).toLocaleString(undefined, {
                maximumFractionDigits: 2,
              })}
              %
            </div>
          ) : (
            <div className="text-lg font-semibold">
              <NullFormatter />
            </div>
          )}
          {metadata?.choice && (
            <div>
              <div className="mb-1 text-xs text-primary-500">Choice</div>
              <div className="text-sm">{metadata.choice}</div>
            </div>
          )}
          {metadata?.rationale && (
            <div>
              <div className="mb-1 text-xs text-primary-500">Rationale</div>
              <MarkdownViewer
                className="py-0 text-sm"
                value={metadata.rationale}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  const spanData = processSpanData(span);
  if (spanData.messages.length === 0) {
    return null;
  }

  return (
    <div className={cn("pb-4")}>
      {head}
      {spanData.messages.length > 0 && (
        <div className="flex flex-col gap-2">
          {spanData.messages.map((message, index) => (
            <div key={`${span.id}-${index}`} id={`message-${span.id}-${index}`}>
              <MessageBubble
                shouldClamp
                message={message}
                toolDefinitions={spanData.toolDefinitions}
                toolResponses={toolResponses}
                idPrefix={`tool-${span.id}-${index}`}
                virtualizer={virtualizer}
                assistantMessageClassName="block w-full"
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
