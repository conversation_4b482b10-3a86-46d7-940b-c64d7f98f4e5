import { isDiffObject } from "#/utils/diffs/diff-objects";
import { useMemo } from "react";
import { z } from "zod";
import {
  type ConfiguredScore,
  type Span,
  type SpanScore,
} from "#/ui/trace/graph";
import { DiffRightField } from "#/utils/diffs/diff-objects";
import {
  type DiscriminatedProjectScore,
  isHumanReviewScore,
} from "@braintrust/local/query";
import { isObject } from "#/utils/object";
import { freeFormDataPath } from "./free-form-text-area";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { getObjValueByPath } from "@braintrust/core";
import { isRootSpan } from "@braintrust/local";

type Args = {
  objectType: DataObjectType;
  projectConfigScores?: DiscriminatedProjectScore[];
  span: Span | null;
};

export function useExtractScores({
  objectType,
  projectConfigScores,
  span,
}: Args) {
  const expectedData = useMemo(() => {
    if (!span) {
      return null;
    }
    const expectedDataJSON = z
      .string()
      .safeParse(
        isDiffObject(span.data.expected)
          ? span.data.expected[DiffRightField]
          : span.data.expected,
      );
    return expectedDataJSON.success ? JSON.parse(expectedDataJSON.data) : null;
  }, [span]);

  const metadataData = useMemo(() => {
    if (!span) {
      return null;
    }

    const metadataRaw = isDiffObject(span.data.metadata)
      ? span.data.metadata[DiffRightField]
      : span.data.metadata;
    const metadataData = metadataRaw
      ? typeof metadataRaw === "string"
        ? JSON.parse(metadataRaw)
        : metadataRaw
      : null;
    const metadataDataParsed = z.record(z.unknown()).safeParse(metadataData);
    return metadataDataParsed.success ? metadataDataParsed.data : null;
  }, [span]);

  const [autoScores, manualScores, numExpectedScores, oldestExpectedScore]: [
    Record<string, SpanScore>,
    Record<string, ConfiguredScore>,
    number,
    DiscriminatedProjectScore | undefined,
  ] = useMemo(() => {
    const hasSpanId = span?.span_id;
    if (!span || !hasSpanId) {
      if (span && !hasSpanId) {
        console.warn("no span id found for span", {
          id: span?.id,
          span_id: span?.span_id,
          span_parents: span?.span_parents,
          root_span_id: span?.root_span_id,
          parent_span_id: span?.parent_span_id,
        });
      }
      return [{}, {}, 0, undefined];
    }

    const scores = span.scores;

    const configuredScores = (projectConfigScores || []).filter((s) =>
      isHumanReviewScore(s.score_type),
    );

    const expectedScores = configuredScores.filter(
      (config) => config.config?.destination === "expected",
    );

    const oldestExpectedScores = [...expectedScores];
    oldestExpectedScores.sort(
      (a, b) =>
        new Date(a.created ?? new Date()).getTime() -
        new Date(b.created ?? new Date()).getTime(),
    );
    const oldestExpectedScore: DiscriminatedProjectScore | undefined =
      oldestExpectedScores[0];

    // TODO: Is scores really a `number`, or could it be a diff object?
    const autoScores: Record<string, SpanScore> = {};
    const manualScores: Record<string, ConfiguredScore> = Object.fromEntries(
      expectedScores
        .map((config) => {
          let toParse = null;
          if (isObject(expectedData)) {
            toParse = expectedData[config.name];
          } else if (
            (expectedScores.length === 1 ||
              config.id === oldestExpectedScore?.id) &&
            Array.isArray(config.categories)
          ) {
            if (
              Array.isArray(expectedData) &&
              expectedData.every((ed) =>
                // @ts-expect-error - asserted as an array above and the object isn't mutated since
                config.categories.some((c) => c.name, ed),
              )
            ) {
              toParse = expectedData;
            } else if (config.categories.some((c) => c.name === expectedData)) {
              toParse = expectedData;
            }
          }

          const expectedParsed = z
            .union([z.string(), z.array(z.string()), z.null()])
            .safeParse(toParse);
          let expected: string[] | undefined = undefined;
          if (expectedParsed.success) {
            expected = Array.isArray(expectedParsed.data)
              ? expectedParsed.data
              : typeof expectedParsed.data === "string"
                ? [expectedParsed.data]
                : undefined;
          }

          return [
            config.name,
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            {
              config,
              left: [],
              right: [],
              isDiff: isDiffObject(span.data.id),
              spanId: z.string().parse(span.span_id),
              expected,
            } as ConfiguredScore,
          ];
        })
        .concat(
          configuredScores
            .filter(
              (config) =>
                config.config?.destination !== "expected" &&
                (objectType !== "dataset" || config.score_type === "free-form"),
            )
            .filter(
              // Show manual scores if they exist on the span, or this is the top-level span
              (config) => isRootSpan(span) || scores[config.name],
            )
            .map((config) => {
              return [
                config.name,
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                {
                  config,
                  ...(scores[config.name] ?? {
                    left: [],
                    right: [],
                    isDiff: isDiffObject(span.data.id),
                    spanId: z.string().parse(span.span_id),
                  }),
                  ...(config.score_type === "free-form" && metadataData
                    ? {
                        freeForm: getObjValueByPath(
                          metadataData,
                          freeFormDataPath({
                            scoreType: config.score_type,
                            destination: config.config?.destination,
                            name: config.name,
                          }).slice(
                            1,
                          ) /* the first element is `metadata`, so we skip it */,
                        ),
                      }
                    : {}),
                } as ConfiguredScore, // This type assertion is needed because typescript can't prove spanId is set
              ];
            }),
        ),
    );

    for (const [k, v] of Object.entries(span.scores)) {
      if (!manualScores[k]) {
        autoScores[k] = v;
      }
    }

    return [
      autoScores,
      manualScores,
      expectedScores.length,
      oldestExpectedScore,
    ];
  }, [expectedData, metadataData, objectType, projectConfigScores, span]);

  return {
    expectedData,
    autoScores,
    manualScores,
    numExpectedScores,
    oldestExpectedScore,
  };
}
