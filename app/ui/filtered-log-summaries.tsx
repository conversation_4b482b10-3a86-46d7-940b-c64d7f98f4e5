import { type CostDetails, crunchOneCost } from "#/app/app/[org]/monitor/costs";
import {
  useBtqlFlags,
  useFeatureFlags,
  useIsFeatureEnabled,
} from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import {
  type DataObjectSearch,
  type DataObjectType,
} from "#/utils/btapi/btapi";
import { fetchBtql } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { useOrg } from "#/utils/user";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { z } from "zod";
import { useAvailableModels } from "./prompts/models";
import {
  type Expr,
  traverseExpr,
  type Shape,
  parseExpr,
} from "@braintrust/btql/parser";
import { experimentEventSchema } from "@braintrust/core/typespecs";
import { type MetricDefinition } from "@braintrust/local/api-schema";
import { doubleQuote } from "#/utils/sql-utils";

export const summarySchema = z
  .object({
    traces: z
      .number()
      .nullish()
      .transform((v) => v ?? 0),
    spans: z
      .number()
      .nullish()
      .transform((v) => v ?? 0),
    p50_latency: z.number().nullish(),
    p90_latency: z.number().nullish(),
    p99_latency: z.number().nullish(),
    p50_llm_latency: z.number().nullish(),
    p90_llm_latency: z.number().nullish(),
    p99_llm_latency: z.number().nullish(),
    p50_tool_latency: z.number().nullish(),
    p90_tool_latency: z.number().nullish(),
    p99_tool_latency: z.number().nullish(),
  })
  // We curate the expressions right now, so we know they will be numbers. But in the future, users might write
  // wacky expressions that are not guaranteed to be numbers, and we might need to evolve this parsing logic.
  .catchall(z.number().nullable());

export const scoresSchema = z.object({
  scores: z.record(
    z.object({
      avg: z.number(),
      num_examples: z.number(),
    }),
  ),
});

export const costSchema = z.object({
  model: z.string().nullish(),
  prompt_uncached_tokens: z.number().nullish(),
  prompt_cached_tokens: z.number().nullish(),
  prompt_cache_creation_tokens: z.number().nullish(),
  completion_tokens: z.number().nullish(),
  count: z.number(),
});

export function useFilteredLogSummaries({
  filters,
  shape,
  metricDefinitions,
  objectId,
  objectType,
}: {
  filters: DataObjectSearch["filters"] | undefined;
  shape: Shape;
  metricDefinitions: MetricDefinition[];
  objectType: DataObjectType;
  objectId: string | null;
}) {
  const org = useOrg();
  const builder = useBtqlQueryBuilder({});
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();

  const projectSummaryMetrics = useIsFeatureEnabled("projectSummaryMetrics");
  const {
    flags: { traceLevelMetrics },
  } = useFeatureFlags();

  const isEnabled =
    !!objectId &&
    !!projectSummaryMetrics &&
    (shape !== "traces" || traceLevelMetrics);

  const summaryResponse = useQuery({
    queryKey: [
      "filtered-summaries",
      objectId,
      objectType,
      filters,
      metricDefinitions,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      if (!objectId) {
        return null;
      }
      try {
        const { data } = await fetchBtql({
          args: {
            query: {
              filter: builder.and(...(filters?.btql ?? [])),
              from: builder.from(objectType, [objectId], shape),
              measures: [
                builder.alias("traces", "sum(is_root)"),
                builder.alias("spans", "count(1)"),
                ...["root", "llm", "tool"].flatMap((type) =>
                  [0.5, 0.9, 0.99].map((percentile) =>
                    builder.alias(
                      `p${percentile * 100}_${type === "root" ? "" : `${type}_`}latency`,
                      `percentile(${type === "root" ? "is_root" : `span_attributes.type = "${type}"`} ? metrics.end-metrics.start : null, ${percentile})`,
                    ),
                  ),
                ),
                ...metricDefinitions
                  .filter((metric) => metric.btql_definition !== null)
                  .map((metric) =>
                    builder.alias(
                      metric.field_name,
                      metric.btql_definition ??
                        `sum(metrics.${doubleQuote(metric.field_name)})`,
                    ),
                  ),
              ],
            },
            brainstoreRealtime: true,
          },
          btqlFlags,
          apiUrl: org.api_url,
          getOrRefreshToken,
          schema: summarySchema,
          signal,
        });
        return data;
      } catch (err) {
        if (isTracesError(err)) {
          return null;
        } else {
          throw err;
        }
      }
    },
    enabled: isEnabled,
  });

  const summaryData = useMemo(() => {
    if (!summaryResponse?.data) {
      return null;
    }
    return summaryResponse.data[0] ?? undefined;
  }, [summaryResponse.data]);

  const costResponse = useQuery({
    queryKey: ["filtered-cost", objectId, objectType, filters],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      if (!objectId) {
        return null;
      }
      try {
        const { data } = await fetchBtql({
          args: {
            query: {
              filter: builder.and(...(filters?.btql ?? [])),
              from: builder.from(objectType, [objectId], shape),
              dimensions: [builder.alias("model", "metadata.model")],
              measures: [
                builder.alias(
                  "prompt_uncached_tokens",
                  "sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)",
                ),
                builder.alias(
                  "prompt_cached_tokens",
                  "sum(metrics.prompt_cached_tokens)",
                ),
                builder.alias(
                  "prompt_cache_creation_tokens",
                  "sum(metrics.prompt_cache_creation_tokens)",
                ),
                builder.alias(
                  "completion_tokens",
                  "sum(metrics.completion_tokens)",
                ),
                builder.alias("count", "count(1)"),
              ],
            },
            brainstoreRealtime: true,
          },
          btqlFlags,
          apiUrl: org.api_url,
          getOrRefreshToken,
          schema: costSchema,
          signal,
        });
        return data;
      } catch (err) {
        if (isTracesError(err)) {
          return null;
        } else {
          throw err;
        }
      }
    },
    enabled: isEnabled,
  });

  const { allAvailableModels } = useAvailableModels({ orgName: org.name });

  const costData = useMemo(() => {
    let costData: CostDetails | null = null;
    for (const item of costResponse?.data ?? []) {
      const itemCost = crunchOneCost({
        data: item,
        allAvailableModels,
      });

      if (!itemCost) {
        continue;
      }
      if (!costData) {
        costData = itemCost;
      } else {
        costData.promptUncachedTokensCost += itemCost.promptUncachedTokensCost;
        costData.promptCachedTokensCost += itemCost.promptCachedTokensCost;
        costData.promptCacheCreationTokensCost =
          itemCost.promptCacheCreationTokensCost;
        costData.completionTokensCost += itemCost.completionTokensCost;
        costData.totalCost += itemCost.totalCost;
        costData.count += itemCost.count;
      }
    }
    return costData;
  }, [allAvailableModels, costResponse.data]);

  const scoresResponse = useQuery({
    queryKey: ["filtered-scores", objectId, objectType, filters],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      if (!objectId) {
        return null;
      }
      try {
        const { data } = await fetchBtql({
          args: {
            query: {
              filter: builder.and(...(filters?.btql ?? [])),
              from: builder.from(objectType, [objectId], shape),
              unpivot: [
                {
                  expr: { btql: "scores" },
                  alias: ["score", "value"],
                },
              ],
              measures: [
                { alias: "last_updated", expr: { btql: "max(created)" } },
                { alias: "num_examples", expr: { btql: "count(1)" } },
                { alias: "avg", expr: { btql: "100*avg(value)" } },
              ],
              pivot: [{ alias: "scores", expr: { btql: "score" } }],
            },
            brainstoreRealtime: true,
          },
          btqlFlags,
          apiUrl: org.api_url,
          getOrRefreshToken,
          schema: scoresSchema,
          signal,
        });
        return data;
      } catch (err) {
        if (isTracesError(err)) {
          return null;
        } else {
          throw err;
        }
      }
    },
    enabled: isEnabled,
  });

  const scoresData = useMemo(() => {
    if (!scoresResponse?.data) {
      return null;
    }
    return scoresResponse.data[0] ?? undefined;
  }, [scoresResponse.data]);

  if (!isEnabled || !summaryData) {
    return null;
  }

  return {
    ...summaryData,
    cost: costData?.totalCost,
    scores: scoresData?.scores,
  };
}

// DEPRECATION_NOTICE: Once all users update to version 1.1.20 or later, then we can remove this
// and the places it's used.
function isTracesError(err: unknown): boolean {
  return (
    err instanceof Error &&
    (err.message.includes("traces") || err.message.includes("Trace"))
  );
}

export function canShowFilteredLogSummaries(btqlFilters: Expr[]): boolean {
  let hasComputedField = false;
  const hasMetricsFilter = (expr: Expr): boolean => {
    if (expr.op === "ident" && expr.name[0] === "metrics") {
      hasComputedField = true;
      return false;
    } else if (
      expr.op === "ident" &&
      typeof expr.name[0] === "string" &&
      !Object.keys(experimentEventSchema.shape).includes(expr.name[0])
    ) {
      hasComputedField = true;
      return false;
    } else if ("btql" in expr) {
      const parsedExpr = parseExpr(expr.btql);
      traverseExpr(parsedExpr, hasMetricsFilter);
      return false;
    } else {
      return true;
    }
  };

  for (const filter of btqlFilters) {
    traverseExpr(filter, hasMetricsFilter);
    if (hasComputedField) {
      return false;
    }
  }
  return true;
}
