"use client";
import React, { type JSX, useLayoutEffect, useState } from "react";
import { cn } from "#/utils/classnames";
import { type BundledLanguage, highlight } from "./highlight";

interface SyntaxHighlightProps {
  language?: BundledLanguage;
  className?: string;
  inline?: boolean;
  content: string;
}

const SyntaxHighlightComponent: React.FC<SyntaxHighlightProps> = ({
  language = "typescript",
  className,
  inline = false,
  content,
}) => {
  const [nodes, setNodes] = useState<JSX.Element | string>(content);

  useLayoutEffect(() => {
    let isMounted = true;
    void highlight({
      code: content,
      lang: language,
    }).then((result) => {
      if (isMounted) {
        setNodes(result);
      }
    });
    return () => {
      isMounted = false;
    };
  }, [content, language]);

  const containerClassName = cn(
    "bg-primary-50 font-mono text-xs",
    inline ? "overflow-x-auto" : "whitespace-pre-wrap",
    className,
  );

  return (
    <div className={containerClassName}>
      <pre
        className={cn("not-prose", inline && "overflow-x-auto no-scrollbar")}
      >
        <code
          className={cn("not-prose shiki", !inline && "whitespace-pre-wrap")}
        >
          {nodes}
        </code>
      </pre>
      {inline && (
        <div className="pointer-events-none absolute inset-y-0 right-0 w-16 scale-95 bg-gradient-to-l from-background" />
      )}
    </div>
  );
};

export const SyntaxHighlight = React.memo(
  SyntaxHighlightComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.language === nextProps.language &&
      prevProps.className === nextProps.className &&
      prevProps.content === nextProps.content
    );
  },
);
