"use client";

import { Tabs, Tab } from "fumadocs-ui/components/tabs";
import { Copy } from "lucide-react";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";

type SupportedLanguage = "ts" | "py" | "typescript" | "python";

export interface ModuleInstallerProps {
  packageNames: string | string[];
  languages?: SupportedLanguage[];
}

function normalizePackages(packageNames: string | string[]): string {
  return Array.isArray(packageNames) ? packageNames.join(" ") : packageNames;
}

function toShortLang(lang: SupportedLanguage): "ts" | "py" {
  if (lang === "typescript" || lang === "ts") return "ts";
  return "py";
}

function InstallBlock({ command }: { command: string }) {
  return (
    <div className="group/codeblock z-10 mb-3 flex w-full items-center gap-2">
      <SyntaxHighlight
        className="flex-1 overflow-hidden rounded-md text-sm bg-transparent"
        language="bash"
        content={command}
      />
      <CopyToClipboardButton
        textToCopy={command}
        size="xs"
        variant="ghost"
        className="opacity-0 transition-opacity text-primary-500 group-hover/codeblock:opacity-100"
      >
        <Copy className="size-3" />
      </CopyToClipboardButton>
    </div>
  );
}

export default function ModuleInstaller({
  packageNames,
  languages = ["ts", "py"],
}: ModuleInstallerProps) {
  const pkgs = normalizePackages(packageNames);
  const langs = Array.from(new Set(languages.map(toShortLang)));

  const tsManagerItems = ["pnpm", "npm", "yarn", "bun"] as const;
  const pyManagerItems = ["uv", "pip"] as const;

  const tsTab = (
    <Tabs
      items={[...tsManagerItems]}
      persist
      groupId="module-installer-ts"
      className="m-0 border-0 [&_[data-state=inactive]]:text-primary-400 [&_[role=tab]]:border-0 [&_[role=tab]]:text-xs [&_[role=tablist]]:p-0 [&_[role=tablist]]:bg-transparent [&_[role=tabpanel]]:p-0"
    >
      <Tab value="pnpm">
        <InstallBlock command={`pnpm add ${pkgs}`} />
      </Tab>
      <Tab value="npm">
        <InstallBlock command={`npm install ${pkgs}`} />
      </Tab>
      <Tab value="yarn">
        <InstallBlock command={`yarn add ${pkgs}`} />
      </Tab>
      <Tab value="bun">
        <InstallBlock command={`bun add ${pkgs}`} />
      </Tab>
    </Tabs>
  );

  const pyTab = (
    <Tabs
      items={[...pyManagerItems]}
      persist
      groupId="module-installer-py"
      className="m-0 border-0 [&_[data-state=inactive]]:text-primary-400 [&_[role=tab]]:border-0 [&_[role=tab]]:text-xs [&_[role=tablist]]:p-0 [&_[role=tablist]]:bg-transparent [&_[role=tabpanel]]:p-0"
    >
      <Tab value="pip">
        <InstallBlock command={`pip install ${pkgs}`} />
      </Tab>
      <Tab value="uv">
        <InstallBlock command={`uv add ${pkgs}`} />
      </Tab>
    </Tabs>
  );

  if (langs.length === 1) {
    return (
      <div className="rounded-lg border px-4 pb-3 bg-primary-50">
        {langs.includes("ts") ? tsTab : pyTab}
      </div>
    );
  }

  return (
    <div className="code-tabs">
      <Tabs
        items={
          langs.includes("ts") && langs.includes("py")
            ? ["TypeScript", "Python"]
            : langs.includes("ts")
              ? ["TypeScript"]
              : ["Python"]
        }
        persist
        groupId="code-tabs"
      >
        {langs.includes("ts") && <Tab value="TypeScript">{tsTab}</Tab>}
        {langs.includes("py") && <Tab value="Python">{pyTab}</Tab>}
      </Tabs>
    </div>
  );
}
