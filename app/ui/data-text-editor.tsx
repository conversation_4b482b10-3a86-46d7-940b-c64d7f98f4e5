import { awesome_line_wrapping_plugin } from "#/ui/codemirror/line-wrapping";
import { cn } from "#/utils/classnames";
import { type TransactionId } from "#/utils/duckdb";
import { isEmpty, isObject } from "#/utils/object";
import { useDarkMode } from "#/utils/useDarkMode";
import { json, jsonParseLinter } from "@codemirror/lang-json";
import { yaml } from "@codemirror/lang-yaml";
import { foldGutter } from "@codemirror/language";
import { linter } from "@codemirror/lint";
import {
  Decoration,
  type DecorationSet,
  EditorView,
  MatchDecorator,
  ViewPlugin,
  type ViewUpdate,
  lineNumbers,
} from "@codemirror/view";
import { indentationMarkers } from "@replit/codemirror-indentation-markers";
import { githubDark, githubLight } from "@uiw/codemirror-theme-github";
import {
  type Dispatch,
  type SetStateAction,
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import TextEditor, {
  type TextEditorHandle,
  type TextEditorProps,
} from "#/ui/text-editor";
import { MarkdownViewer } from "#/ui/markdown";
import { SandpackViewer } from "#/ui/sandpack/viewer";
import { useOptimisticState } from "#/utils/optimistic-update";
import { TreeViewer, type TreeViewerHandle } from "#/ui/tree";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { LLMMessageView } from "#/ui/LLMView";
import { ParseModeSelect } from "#/ui/parse-mode-select";
import { SchemaBuilder } from "#/ui/schema-builder";
import {
  Clipboard,
  Eye,
  FoldVertical,
  TriangleAlert,
  UnfoldVertical,
} from "lucide-react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "#/ui/tooltip";
import { Button } from "#/ui/button";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { urlMatcherRegexp } from "#/utils/url";
import {
  type CopilotContextFormat,
  type CopilotEditorContext,
} from "@braintrust/local/copilot";
import { clearLocalCache as clearCopilotCache } from "codemirror-copilot";
import type { ApplySearch } from "./use-filter-sort-search";
import { type EditorState, StateEffect, StateField } from "@codemirror/state";
import { useSelectedEditorLineState } from "./query-parameters";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  isAllDataRenderOption,
  deserializeValue as deserializeValueRaw,
  type RenderOption,
  renderOptionNames,
  renderOptionNamesSchema,
  renderOptions,
} from "#/utils/parse";
import { AttachmentListFromData } from "#/ui/attachment/attachment-list";
import {
  ensureParsedSyntaxTree,
  getAllFoldPaths,
} from "./codemirror/codemirror-fold";
import { type TraceViewParams } from "./trace/trace";
import { type FieldValue } from "./schema-node";

// References:
//  - https://discuss.codemirror.net/t/detect-urls/3595
//  - https://discuss.codemirror.net/t/decorated-a-tags-nor-treated-as-links-by-browser/3664
//  - https://codemirror.net/examples/decoration/
const urlMatcher = new MatchDecorator({
  regexp: urlMatcherRegexp,
  decoration: (match) =>
    Decoration.mark({
      tagName: "a",
      attributes: {
        href: match[0],
        target: "_blank",
        class: "cmt-url cm-underline",
      },
    }),
});

const urlLinker = ViewPlugin.fromClass(
  class {
    placeholders: DecorationSet;
    constructor(view: EditorView) {
      this.placeholders = urlMatcher.createDeco(view);
    }
    update(update: ViewUpdate) {
      this.placeholders = urlMatcher.updateDeco(update, this.placeholders);
    }
  },
  {
    decorations: (instance) => instance.placeholders,
    provide: (plugin) =>
      EditorView.decorations.of((view) => {
        return view.plugin(plugin)?.placeholders || Decoration.none;
      }),
  },
);

export type TextEditorValue =
  | Record<string, unknown>
  | string
  | null
  | undefined;
export const UpdateableDataTextEditor = ({
  value: initialValue,
  diffValue,
  updateValue,
  rowId,
  xactId,
  className,
  formatOnBlur,
  selectedRenderOption,
  setSelectedRenderOption,
  onApplySearch,
  allowedRenderOptions,
  isReadOnly,
  searchQuery,
  makeCopilotContext,
  foldStateId,
  selectedLineId,
  colorSwatchClassName,
  attachmentsLocalStorageKey,
  extraActions,
  onChange,
  diffMessage,
  experimentNames,
  onBlur,
  validateFn,
}: {
  value: TextEditorValue;
  diffValue?: TextEditorValue;
  updateValue:
    | ((value: TextEditorValue) => Promise<TransactionId | null>)
    | undefined;
  rowId: string;
  xactId: TransactionId | null;
  className?: string;
  formatOnBlur?: boolean;
  onApplySearch?: ApplySearch;
  allowedRenderOptions?: RenderOption[];
  isReadOnly?: boolean;
  searchQuery?: string;
  makeCopilotContext?: DataEditorCopilotContextFn;
  foldStateId?: string;
  selectedLineId?: string;
  colorSwatchClassName?: string;
  attachmentsLocalStorageKey?: string;
  extraActions?: React.ReactNode;
  onChange?: (value: TextEditorValue) => void;
  experimentNames?: TraceViewParams["experimentNames"];
  diffMessage?: string;
  onBlur?: React.FocusEventHandler<HTMLDivElement>;
  validateFn?: (value: unknown) => void;
} & RenderOptionProps) => {
  isReadOnly = isReadOnly || !updateValue;
  const hasUpdateValue = !!updateValue;

  const { save, value: mergedValue } = useOptimisticState({
    xactId,
    value: initialValue,
    save: updateValue || (() => Promise.resolve(null)),
    rowKey: rowId,
  });

  const saveValue = useCallback(
    async (value: TextEditorValue) => {
      if (!hasUpdateValue) {
        return;
      }

      if (
        (mergedValue === undefined && value === undefined) ||
        JSON.stringify(mergedValue) === JSON.stringify(value)
      ) {
        return;
      }

      const ret = await save(value ?? null);
      clearCopilotCache();
      return ret;
    },
    [hasUpdateValue, save, mergedValue],
  );

  return (
    <div className={cn("flex flex-col", { "gap-2": !isReadOnly })}>
      <DataTextEditor
        rowId={rowId}
        value={mergedValue}
        diffValue={diffValue}
        onSave={saveValue}
        className={className}
        readOnly={isReadOnly}
        formatOnBlur={formatOnBlur}
        selectedRenderOption={selectedRenderOption}
        setSelectedRenderOption={setSelectedRenderOption}
        onApplySearch={onApplySearch}
        allowedRenderOptions={allowedRenderOptions}
        searchQuery={searchQuery}
        makeCopilotContext={makeCopilotContext}
        foldStateId={foldStateId}
        selectedLineId={selectedLineId}
        colorSwatchClassName={colorSwatchClassName}
        extraActions={extraActions}
        onChange={onChange}
        experimentNames={experimentNames}
        diffMessage={diffMessage}
        onBlur={onBlur}
        validateFn={validateFn}
      />
      <AttachmentListFromData
        data={mergedValue}
        localStorageKey={attachmentsLocalStorageKey}
      />
    </div>
  );
};

export interface RenderOptionProps {
  selectedRenderOption?: RenderOption;
  setSelectedRenderOption?: Dispatch<SetStateAction<RenderOption | undefined>>;
}

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
export const EDITABLE_DATA_MODES = [
  "yaml",
  "json",
  "text",
  "schema-builder",
] as RenderOption[];

export type DataEditorCopilotContextFn = (
  fmt: CopilotContextFormat,
) => CopilotEditorContext | undefined;

type DataEditorProps = {
  rowId?: string;
  value: unknown;
  diffValue?: unknown;
  formatOnBlur?: boolean;
  allowedRenderOptions?: RenderOption[];
  onApplySearch?: ApplySearch;
  searchQuery?: string;
  makeCopilotContext?: DataEditorCopilotContextFn;
  foldStateId?: string;
  selectedLineId?: string;
  onChange?: (value: TextEditorValue) => void;
  onError?: (error: Error | null) => void;
  colorSwatchClassName?: string;
  extraActions?: React.ReactNode;
  onSave?: (value: TextEditorValue) => void;
  experimentNames?: TraceViewParams["experimentNames"];
  forceError?: boolean;
  validateFn?: (value: unknown) => void;
  diffMessage?: string;
  hideLineNumbers?: boolean;
  forceRefresh?: boolean;
} & RenderOptionProps &
  Pick<
    TextEditorProps,
    | "className"
    | "readOnly"
    | "onMetaEnter"
    | "autoFocus"
    | "placeholder"
    | "tabAutocomplete"
    | "onBlur"
  >;

//TODO: Investigate why we have multiple re-renders on value change
export const DataTextEditor = memo(
  forwardRef<TextEditorHandle<unknown>, DataEditorProps>(
    (
      {
        rowId,
        value: valueProp,
        diffValue,
        formatOnBlur,
        className,
        readOnly,
        selectedRenderOption,
        setSelectedRenderOption,
        allowedRenderOptions,
        onApplySearch,
        searchQuery,
        makeCopilotContext,
        foldStateId,
        selectedLineId,
        onError,
        colorSwatchClassName,
        extraActions,
        onSave,
        onChange: onChangeProp,
        experimentNames,
        forceError,
        validateFn,
        hideLineNumbers,
        forceRefresh,
        ...remaining
      }: DataEditorProps,
      ref,
    ) => {
      const { flags } = useFeatureFlags();
      const [preferredParseMode] = useEntityStorage({
        entityType: "app",
        entityIdentifier: "data-view",
        key: "parseMode",
      });
      const darkMode = useDarkMode();
      const [_parseModeState, _setParseModeState] = useState<
        RenderOption | undefined
      >(undefined);

      const _parseMode = selectedRenderOption ?? _parseModeState;
      const setParseMode = setSelectedRenderOption ?? _setParseModeState;

      const [value, setValue] = useState(valueProp);

      useEffect(() => {
        setValue(valueProp);
      }, [valueProp]);

      const isValueTooLarge = useMemo(() => {
        if (!value || typeof value === "string") {
          return false;
        }
        return JSON.stringify(value).length > 10000;
      }, [value]);

      const [treeViewSizeOverride, setTreeViewSizeOverride] = useState(false);
      const [hasUnsupportedSchemaIssues, setHasUnsupportedSchemaIssues] =
        useState(false);

      const validParseModes = useMemo(
        () =>
          renderOptionNames.filter(
            (name) =>
              renderOptions[name].filter(value) &&
              (isEmpty(allowedRenderOptions) ||
                allowedRenderOptions.includes(name)) &&
              // schema-builder should only show up if explicitly allowed
              (name !== "schema-builder" ||
                (allowedRenderOptions && allowedRenderOptions.includes(name))),
          ),
        [allowedRenderOptions, value],
      );

      const parseMode = useMemo(() => {
        const preferredParseModes = validParseModes.filter(
          (name) =>
            // if the editor is not readOnly, do not prefer readOnly parse modes
            renderOptions[name].guess(value) &&
            (readOnly || !renderOptions[name].readOnly),
        );
        if (_parseMode === undefined || !validParseModes.includes(_parseMode)) {
          const preferredOptParsed =
            renderOptionNamesSchema.safeParse(preferredParseMode);
          const defaultParseMode =
            (isEmpty(allowedRenderOptions) && preferredParseModes[0]) ||
            validParseModes[0];
          if (
            preferredOptParsed.success &&
            validParseModes.includes(preferredOptParsed.data) &&
            isAllDataRenderOption(renderOptions[defaultParseMode])
          ) {
            return preferredOptParsed.data;
          }
          return defaultParseMode;
        } else {
          return _parseMode;
        }
      }, [
        _parseMode,
        validParseModes,
        value,
        preferredParseMode,
        allowedRenderOptions,
        readOnly,
      ]);

      const serializeValue = useCallback(
        (v: unknown) => {
          const serialize = renderOptions[parseMode]?.serialize;
          return serialize ? serialize(v) : "<invalid>";
        },
        [parseMode],
      );

      const deserializeValue = useCallback(
        (valueString: string) => {
          let v: unknown;
          try {
            v = deserializeValueRaw(valueString, parseMode);
            setError(null);
          } catch (e) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            setError(e as Error);
            throw e;
          }

          if (validateFn) {
            try {
              validateFn(v);
            } catch (e) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              setError(e as Error);
              throw e;
            }
          }

          return v;
        },
        [parseMode, validateFn],
      );

      const processedValue = useMemo(
        () => serializeValue(value),
        [serializeValue, value],
      );

      const processedDiffValue = useMemo(() => {
        if (diffValue === undefined) return undefined;
        const serializedDiffValue = serializeValue(diffValue);
        // the text parse mode can sometimes serialize as an object
        return isObject(serializedDiffValue) && parseMode === "text"
          ? JSON.stringify(serializedDiffValue, null, 2)
          : serializedDiffValue;
      }, [serializeValue, diffValue, parseMode]);

      const setEditorSelectedLine = useCallback((line?: number) => {
        editorRef.current?.editorRef()?.view?.dispatch({
          effects: setSelectedLine.of(line),
        });
      }, []);

      const [selectedLineNumber, setSelectedLineNumber] =
        useSelectedEditorLineState(selectedLineId);

      useEffect(() => {
        if (selectedLineNumber === null) {
          setEditorSelectedLine();
        }
      }, [selectedLineNumber, setEditorSelectedLine]);

      const onClickLineNumber = useCallback(
        (line: number) => {
          if (!selectedLineId) return;
          const shouldClear = selectedLineNumber === line;
          setSelectedLineNumber(shouldClear ? null : line);
          setEditorSelectedLine(shouldClear ? undefined : line);
        },
        [
          selectedLineId,
          setSelectedLineNumber,
          selectedLineNumber,
          setEditorSelectedLine,
        ],
      );

      const extensions = useMemo(() => {
        const extensionsArray = [];
        if (!hideLineNumbers) {
          extensionsArray.push(
            lineNumbers({
              domEventHandlers: {
                click: (view, line) => {
                  const fromLine = view.state.doc.lineAt(line.from).number;
                  onClickLineNumber(fromLine);
                  return true;
                },
              },
            }),
            selectedLineField,
            urlLinker,
          );
        }
        if (parseMode === "yaml") {
          extensionsArray.push(yaml());
        } else if (parseMode === "json") {
          extensionsArray.push(json(), linter(jsonParseLinter()));
        }
        extensionsArray.push(
          foldGutter(),
          darkMode ? githubDark : githubLight,
          ViewPlugin.fromClass(
            class {
              constructor(view: EditorView) {}

              update(viewUpdate: ViewUpdate) {
                ensureParsedSyntaxTree(viewUpdate.state, viewUpdate.view);
              }

              destroy() {}
            },
          ),
        );

        // This is from https://discuss.codemirror.net/t/making-codemirror-6-respect-indent-for-wrapped-lines/2881/4
        extensionsArray.push(...awesome_line_wrapping_plugin);
        extensionsArray.push(
          indentationMarkers({
            highlightActiveBlock: false,
            colors: {
              light: "rgba(204, 204, 204, 0.5)",
              dark: "rgba(61, 61, 61, 0.5)",
            },
          }),
        );
        return extensionsArray;
      }, [darkMode, parseMode, onClickLineNumber, hideLineNumbers]);

      const editorRef = useRef<TextEditorHandle>(null);

      const onSaveParsed = useCallback(
        (value: string | undefined) => {
          let parsedValue: TextEditorValue;
          try {
            parsedValue = getParsedValue(value, parseMode);
            setError(null);
          } catch (e) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            setError(e as Error);
            console.warn(
              `Failed to parse ${parseMode} value from editor, likely because it's invalid. Will not save`,
              e,
            );
            return;
          }

          if (validateFn) {
            try {
              validateFn(parsedValue);
            } catch (e) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              setError(e as Error);
              console.warn(
                `Failed to validate ${parseMode} value from editor, likely because it's invalid. Will not save`,
                e,
              );
              return;
            }
          }

          return onSave?.(parsedValue);
        },
        [onSave, parseMode, validateFn],
      );

      useImperativeHandle(
        ref,
        () => ({
          getValue(): unknown | undefined {
            const valueString = editorRef.current?.getValue();
            return getParsedValue(valueString, parseMode);
          },
          setValue(value: unknown) {
            editorRef.current?.setValue(serializeValue(value) ?? "");
          },
          focus(up): void {
            editorRef.current?.focus(up);
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
          getLintErrors(): any[] {
            return editorRef.current?.getLintErrors() ?? [];
          },
          editorRef: () => editorRef.current?.editorRef() ?? null,
        }),
        [serializeValue, parseMode],
      );

      const treeRef = useRef<TreeViewerHandle>(null);

      const isCollapsible =
        parseMode !== "llm" &&
        parseMode !== "llm-raw" &&
        parseMode !== "schema-builder" &&
        (isObject(value) || value instanceof Array || parseMode === "tree");
      // Ideally we would use the foldState to handle collapsing, but
      // different parse modes have different fold areas, so we will use isCollapsed
      // handle fully-collapsed states when switching between parse modes and rows
      const [isCollapsed, setIsCollapsed] = useState(false);

      const [foldState, _setFoldState] = useEntityStorage({
        entityType: "dataEditor",
        entityIdentifier: foldStateId || "",
        key: "foldState",
      });

      const setFoldState = useCallback<typeof _setFoldState>(
        (updater) => {
          _setFoldState(updater);
          setIsCollapsed(false);
        },
        [_setFoldState, setIsCollapsed],
      );

      const copyableValue = useMemo(() => {
        if (typeof value === "string") {
          return value;
        }
        if (typeof processedValue === "string") {
          if (processedValue === "<invalid>" && typeof value === "object") {
            try {
              return JSON.stringify(value, null, 2);
            } catch (e) {
              return null;
            }
          }

          return processedValue;
        }
        return null;
      }, [value, processedValue]);

      const [error, setError] = useState<Error | null>(null);

      useEffect(() => {
        onError?.(error);
      }, [error, onError]);

      const getAutoCompleteContext = useMemo(() => {
        if (!makeCopilotContext || readOnly || !flags.copilotData) {
          return undefined;
        }
        return () =>
          makeCopilotContext(
            parseMode === "json"
              ? "json"
              : parseMode === "yaml"
                ? "yaml"
                : "text",
          );
      }, [makeCopilotContext, parseMode, readOnly, flags.copilotData]);

      const onSelectParseMode = useCallback(
        (option: RenderOption) => {
          const valueString = editorRef.current?.getValue();
          // non-text editors may display the text string "null" for null values, so ignore them as text
          const hasNonNullValue =
            valueString &&
            (parseMode === "text" || valueString.toLowerCase() !== "null");
          // if original value is null we will preserve it if text hasn't changed
          if (!isEmpty(valueString) && (value != null || hasNonNullValue)) {
            setValue(deserializeValue(valueString));
          }

          // clear selected line
          setSelectedLineNumber(null);
          setEditorSelectedLine(undefined);

          setParseMode(option);
        },
        [
          setValue,
          deserializeValue,
          parseMode,
          value,
          setSelectedLineNumber,
          setEditorSelectedLine,
          setParseMode,
        ],
      );

      const formatter = useMemo(() => {
        if (!formatOnBlur) return undefined;
        return (value: string) => {
          try {
            return serializeValue(deserializeValue(value)) ?? "";
          } catch {
            return value;
          }
        };
      }, [formatOnBlur, serializeValue, deserializeValue]);

      const onFoldClick = useCallback(
        (e: React.MouseEvent) => {
          e.preventDefault();
          const treePaths = treeRef.current?.getAllFoldPaths() ?? [];
          const editorView = editorRef.current?.editorRef()?.view;
          const editorPaths = editorView ? getAllFoldPaths(editorView) : [];
          _setFoldState((prev) =>
            Object.fromEntries([
              ...[...Object.keys(prev), ...treePaths].map((v) => [
                v,
                !isCollapsed,
              ]),
              ...editorPaths.map(({ path }) => [path, !isCollapsed]),
            ]),
          );
          setIsCollapsed(!isCollapsed);
        },
        [treeRef, editorRef, _setFoldState, isCollapsed],
      );

      const onCreateCodeMirrorEditor = useCallback(
        (view: EditorView, state: EditorState) => {
          if (!selectedLineNumber) return;
          if (selectedLineNumber > state.doc.lines) {
            setSelectedLineNumber(null);
            return;
          }
          const line = state.doc.line(selectedLineNumber);

          setTimeout(() => {
            view.dispatch({
              selection: { head: line.from, anchor: line.to },
              scrollIntoView: true,
              effects: setSelectedLine.of(selectedLineNumber),
            });
          }, 100);
        },
        [selectedLineNumber, setSelectedLineNumber],
      );

      const onChange = useCallback(
        (value: string) => {
          try {
            const parsedValue = getParsedValue(value, parseMode);
            onChangeProp?.(parsedValue);
          } catch (e) {
            // ignore
          }
        },
        [onChangeProp, parseMode],
      );

      const experimentName =
        colorSwatchClassName && colorSwatchClassName.startsWith("bg-primary")
          ? experimentNames?.current
          : experimentNames?.comparison;

      const experimentSwatch = colorSwatchClassName && (
        <div
          className={cn("size-2 flex-none rounded-full", colorSwatchClassName)}
        />
      );

      return (
        <div className="group flex grow items-stretch">
          <div
            className={cn(
              "flex flex-col flex-grow w-full",
              // we don't usually need to specify a dark: variant for primary color,
              // but in this case "50" is hard to perceive in dark mode
              {
                "bg-primary-50 dark:bg-primary-100 border rounded-md":
                  !readOnly,
                "cursor-text":
                  !readOnly &&
                  parseMode !== "markdown" &&
                  parseMode !== "html" &&
                  parseMode !== "tree",
                "border-bad-200 bg-bad-50":
                  forceError ||
                  (error &&
                    !readOnly &&
                    parseMode !== "markdown" &&
                    parseMode !== "html" &&
                    parseMode !== "tree"),
                "linkable-gutter": !!selectedLineId,
              },
              className,
            )}
            onMouseUp={() => {
              if (readOnly) return;
              editorRef.current?.focus(false);
            }}
          >
            <div
              className={cn("flex gap-2 items-center overflow-hidden", {
                "p-1 pb-0": !readOnly,
              })}
            >
              <div className="flex flex-1 items-center gap-2">
                {colorSwatchClassName && (
                  <BasicTooltip tooltipContent={experimentName}>
                    {experimentSwatch}
                  </BasicTooltip>
                )}
                <div
                  className={cn(
                    "flex items-center gap-2",
                    hasUnsupportedSchemaIssues &&
                      parseMode === "schema-builder" &&
                      "[animation-duration:1.3s] animate-pulse",
                  )}
                >
                  <ParseModeSelect
                    value={parseMode}
                    options={validParseModes}
                    isFieldReadOnly={readOnly}
                    onSelect={onSelectParseMode}
                    showSetDefaultMessage={preferredParseMode === undefined}
                  />
                </div>
              </div>
              <div className="flex flex-1 items-center justify-end gap-1 opacity-0 transition-opacity group-hover:opacity-100">
                {isCollapsible && (
                  <BasicTooltip
                    tooltipContent={isCollapsed ? "Expand all" : "Collapse all"}
                  >
                    <Button
                      size="xs"
                      variant="ghost"
                      className="text-primary-400"
                      onClick={onFoldClick}
                      Icon={isCollapsed ? UnfoldVertical : FoldVertical}
                    />
                  </BasicTooltip>
                )}
                {copyableValue && (
                  <CopyToClipboardButton
                    size="xs"
                    variant="ghost"
                    className="text-primary-400"
                    textToCopy={copyableValue}
                  />
                )}
                {extraActions}
                {error && (
                  <Tooltip delayDuration={0} disableHoverableContent>
                    <TooltipTrigger asChild>
                      <div className="flex size-5 items-center justify-center text-bad-500">
                        <TriangleAlert className="size-3" />
                      </div>
                    </TooltipTrigger>
                    <TooltipPortal>
                      <TooltipContent
                        side="bottom"
                        align="end"
                        className="max-w-sm text-xs"
                      >
                        {error.message}
                        <span className="block pt-2 text-xs text-primary-500">
                          This value will not be saved
                        </span>
                      </TooltipContent>
                    </TooltipPortal>
                  </Tooltip>
                )}
              </div>
            </div>
            {parseMode === "markdown" ? (
              <MarkdownViewer
                className={cn({
                  "px-2": !readOnly,
                })}
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                value={value as string}
              />
            ) : parseMode === "html" ? (
              <SandpackViewer value={value} />
            ) : parseMode === "tree" ? (
              <div className="p-2 text-sm">
                {isValueTooLarge && !treeViewSizeOverride ? (
                  <div className="flex flex-col items-center justify-center gap-1.5 text-sm text-primary-600">
                    <div className="text-center text-primary-500">
                      Data is too large to display in tree view. Please choose
                      another view type or copy the value to your clipboard.
                    </div>
                    <div className="flex flex-row gap-2">
                      {copyableValue && (
                        <CopyToClipboardButton
                          size="xs"
                          Icon={Clipboard}
                          textToCopy={copyableValue}
                        >
                          Copy to clipboard
                        </CopyToClipboardButton>
                      )}
                      <Button
                        size="xs"
                        variant="ghost"
                        Icon={Eye}
                        onClick={() => setTreeViewSizeOverride(true)}
                      >
                        Show it anyway
                      </Button>
                    </div>
                  </div>
                ) : (
                  <TreeViewer
                    value={value}
                    ref={treeRef}
                    onApplySearch={onApplySearch}
                    foldState={foldStateId ? foldState : undefined}
                    setFoldState={foldStateId ? setFoldState : undefined}
                    isCollapsed={isCollapsed}
                  />
                )}
              </div>
            ) : parseMode === "llm" ? (
              <LLMMessageView value={value} />
            ) : parseMode === "llm-raw" ? (
              <LLMMessageView value={value} isRaw />
            ) : parseMode === "text" ||
              parseMode === "yaml" ||
              parseMode === "json" ? (
              <TextEditor
                className="flex grow text-xs"
                key={parseMode}
                searchQuery={searchQuery}
                value={processedValue ?? undefined}
                diffValue={processedDiffValue ?? undefined}
                wrap
                styled
                extensions={extensions}
                ref={editorRef}
                formatter={formatter}
                onCreateEditor={onCreateCodeMirrorEditor}
                readOnly={readOnly}
                foldState={foldStateId ? foldState : undefined}
                setFoldState={foldStateId ? setFoldState : undefined}
                isCollapsed={isCollapsed}
                placeholder={readOnly ? undefined : "Enter value"}
                getAutoCompleteContext={getAutoCompleteContext}
                onSave={onSaveParsed}
                onChange={onChange}
                {...remaining}
              />
            ) : parseMode === "schema-builder" ? (
              <div className="px-1 py-2">
                <SchemaBuilder
                  value={(() => {
                    if (typeof value === "object" && value !== null) {
                      return value;
                    }
                    if (typeof value === "string") {
                      try {
                        return JSON.parse(value);
                      } catch {
                        return {};
                      }
                    }
                    return {};
                  })()}
                  onChange={(newValue: FieldValue) => {
                    // Convert FieldValue to TextEditorValue (which is more restrictive)
                    let textEditorValue: TextEditorValue;
                    if (
                      typeof newValue === "number" ||
                      typeof newValue === "boolean"
                    ) {
                      textEditorValue = JSON.stringify(newValue);
                    } else if (Array.isArray(newValue)) {
                      // Convert array to object with numeric keys
                      const obj: Record<string, unknown> = {};
                      newValue.forEach((item, index) => {
                        obj[index.toString()] = item;
                      });
                      textEditorValue = obj;
                    } else {
                      textEditorValue = newValue;
                    }
                    setValue(textEditorValue);
                    onChangeProp?.(textEditorValue);
                    onSave?.(textEditorValue);
                  }}
                  readOnly={readOnly}
                  forceRefresh={forceRefresh}
                  onUnsupportedIssuesChange={setHasUnsupportedSchemaIssues}
                />
              </div>
            ) : (
              "Invalid parse mode"
            )}
          </div>
        </div>
      );
    },
  ),
);
DataTextEditor.displayName = "DataTextEditor";

const setSelectedLine = StateEffect.define<number | undefined>();
const selectedLineField = StateField.define({
  create() {
    return Decoration.none;
  },
  update(highlights, tr) {
    highlights = highlights.map(tr.changes);
    for (const effect of tr.effects) {
      if (effect.is(setSelectedLine)) {
        if (effect.value === undefined) {
          return Decoration.none;
        }
        const line = tr.state.doc.line(effect.value);
        return Decoration.set(selectedLineDecoration.range(line.from));
      }
    }
    return highlights;
  },
  provide: (f) => EditorView.decorations.from(f),
});
const selectedLineDecoration = Decoration.line({
  attributes: { class: "cm-selectedLine" },
});

function getParsedValue(value: string | undefined, parseMode: RenderOption) {
  if (isEmpty(value)) {
    return undefined;
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return deserializeValueRaw(value, parseMode) as TextEditorValue;
}
