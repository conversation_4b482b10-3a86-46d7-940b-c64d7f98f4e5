"use client";

import { type Dispatch, type RefObject, type SetStateAction } from "react";
import { FunctionEditor } from "#/ui/prompts/function-editor/function-editor";
import {
  FunctionDescriptionField,
  FunctionMetadataField,
  FunctionNameField,
  FunctionSlugField,
} from "#/ui/prompts/function-editor/function-meta-fields";
import { usePromptExtensions } from "#/ui/prompts/hooks";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import { promptSchema, type UIFunction } from "#/ui/prompts/schema";
import { type MetaFields } from "#/ui/prompts/function-editor/types";
import { produce } from "immer";
import { zodErrorToString } from "#/utils/validation";
import { type ZodError } from "zod";

const OUTPUT_NAMES: string[] = [];

type Props = {
  type: FunctionObjectType;
  func: UIFunction;
  modeType: "create" | "update";
  metaFields: MetaFields;
  setMetaFields: Dispatch<SetStateAction<MetaFields>>;
  isSlugTouchedRef: RefObject<boolean>;
  setError: Dispatch<SetStateAction<string | null>>;
};

export default function FunctionDetailEditor({
  type,
  func,
  modeType,
  metaFields,
  setMetaFields,
  isSlugTouchedRef,
  setError,
}: Props) {
  const org = useOrg();
  const orgName = org.name;

  const isUpdate = modeType === "update";

  const { extensions } = usePromptExtensions({
    jsonStructure: null,
    outputNames: OUTPUT_NAMES,
  });

  return (
    <div className="flex flex-auto flex-col overflow-y-auto px-3 py-4">
      <div className="flex flex-col gap-3">
        <div className="flex w-full gap-3">
          <FunctionNameField
            className="flex-1"
            name={metaFields.name ?? ""}
            isUpdate={isUpdate}
            onChange={({ name, slug }) => {
              setMetaFields(
                produce((prev) => {
                  prev.name = name;
                  if (slug && !isSlugTouchedRef.current) {
                    prev.slug = slug;
                  }
                }),
              );
            }}
          />
          <FunctionSlugField
            className="flex-1"
            slug={metaFields.slug ?? ""}
            onChange={(newSlug) => {
              setMetaFields(
                produce((prev) => {
                  prev.slug = newSlug;
                }),
              );
              //eslint-disable-next-line react-compiler/react-compiler
              isSlugTouchedRef.current = true;
            }}
          />
        </div>
        <FunctionEditor
          orgName={orgName}
          type={type}
          modeType={modeType}
          extensions={extensions}
        />
        <FunctionDescriptionField
          description={metaFields.description ?? ""}
          onChange={(description) => {
            setMetaFields(
              produce((prev) => {
                prev.description = description;
              }),
            );
          }}
        />
        <FunctionMetadataField
          metadata={metaFields.metadata}
          onChange={async (metadata) => {
            //   TODO: debounce parsing?
            setMetaFields(
              produce((prev) => {
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                prev.metadata = metadata as Record<string, unknown> | null;
              }),
            );
            try {
              promptSchema.shape.metadata.parse(metadata);
              setError(null);
            } catch (error) {
              setError(
                "Invalid metadata: " +
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  zodErrorToString(error as ZodError, 0, false),
              );
            }
            return null;
          }}
          functionId={func.id}
        />
      </div>
    </div>
  );
}
