"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useContext, useRef, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { useIsSidenavDocked } from "#/app/app/[org]/sidenav-state";
import { useFunctionEditorPrompt } from "#/ui/prompts/function-editor/use-function-editor-prompt";
import { useAvailableModels } from "#/ui/prompts/models";
import { Button } from "#/ui/button";
import {
  GalleryVerticalEnd,
  MessageCircle,
  MessagesSquare,
  Percent,
  Play,
  Plus,
} from "lucide-react";
// intentionally using unstyled radix tabs instead of our own styled variants because it's easier than rampantly overriding all the default styling
import { Tabs<PERSON>rigger, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>, Ta<PERSON>Content } from "@radix-ui/react-tabs";
import { PromptChat } from "#/ui/prompts/chat/prompt-chat";
import FunctionDetailEditor from "#/ui/prompts/function-detail/function-detail-editor";
import { useOrg } from "#/utils/user";
import {
  type ObjectType,
  type MetaFields,
} from "#/ui/prompts/function-editor/types";
import { useOutputPrompt } from "#/ui/prompts/function-editor/use-output-prompt";
import { createOrUpdatePrompt, type UIFunction } from "#/ui/prompts/schema";
import { toast } from "sonner";
import { FunctionDetailActionButtons } from "#/ui/prompts/function-detail/function-detail-action-buttons";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { capitalize, prettifyXact } from "@braintrust/core";
import { deleteRow, type DML } from "#/utils/mutable-object";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { useRouter } from "next/navigation";
import { slugify } from "#/utils/slug";
import { newId } from "braintrust";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { usePromptTabState } from "#/ui/query-parameters";
import { FunctionDetailActivity } from "#/ui/prompts/function-detail/function-detail-activity";
import { FunctionDetailConfirmSaveDialog } from "#/ui/prompts/function-detail/function-detail-confirm-save-dialog";
import { getProjectLink } from "#/app/app/[org]/p/[project]/getProjectLink";
import { SyncedPromptsProvider } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { FunctionDetailRun } from "./function-detail-run";
import { useEntityStorage } from "#/lib/clientDataStorage";
import {
  CreateExperimentDialog,
  type CreateExperimentTask,
} from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { ScorersDropdownProvider } from "#/app/app/[org]/p/[project]/playgrounds/scorers-dropdown";
import { ProjectPromptsDropdownProvider } from "#/app/app/[org]/p/[project]/playgrounds/prompts-dropdown";

type Props = {
  func: UIFunction | null;
  dml: DML;
  type: FunctionObjectType;
  projectId: string;
  auditLogScan: string | null;
  auditLogReady: number[];
  initialTab?: "run" | "chat" | "activity";
  availableTabs?: ("run" | "chat" | "activity")[];
  objectType: ObjectType;
};

export function FunctionDetail({
  func,
  dml,
  type,
  projectId,
  auditLogScan,
  auditLogReady,
  initialTab,
  availableTabs,
  objectType,
}: Props) {
  const isSidenavDocked = useIsSidenavDocked();
  const router = useRouter();

  const { projectName } = useContext(ProjectContext);
  const org = useOrg();
  const orgName = org.name;

  const modeType = func === null ? "create" : "update";
  const isUpdate = modeType === "update";

  const { allAvailableModels } = useAvailableModels({ orgName });

  const [tab, setTab] = usePromptTabState(initialTab);

  const { promptState, coercedFunction } = useFunctionEditorPrompt({
    initialFunction: func,
    type,
    projectId,
    modeType: modeType,
  });

  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metaFields, setMetaFields] = useState<MetaFields>({
    name: coercedFunction.name,
    slug: coercedFunction.slug,
    description: coercedFunction.description,
    metadata: coercedFunction.metadata,
  });
  const isSlugTouchedRef = useRef(false);
  const [isDirty, setIsDirty] = useState(false);

  const entityIdentifier = modeType === "create" ? "new" : coercedFunction.id;
  const [runData, setRunData] = useEntityStorage({
    entityType: "functions",
    entityIdentifier,
    key: "functionDetailRunData",
  });
  const [extraMessagesPath, setExtraMessagesPath] = useEntityStorage({
    entityType: "functions",
    entityIdentifier,
    key: "functionDetailExtraMessagesPath",
  });

  const { functions: scorerFunctions } = useScorerFunctions({});
  const defaultScorers =
    coercedFunction.function_type === "scorer" && coercedFunction.id
      ? [{ type: "function" as const, id: coercedFunction.id }]
      : [];

  const getOutputPrompt = useOutputPrompt({
    coercedFunction,
    metaFields,
  });

  const upsert = async (prompt: UIFunction, updateSlug: boolean) => {
    if (!projectId || !org.id) {
      throw new Error("Missing project or org context");
    }

    try {
      const result = await createOrUpdatePrompt({
        dml,
        orgId: org.id,
        update: isUpdate,
        updateSlug,
        prompt: {
          ...prompt,
          project_id: projectId,
        },
      });

      if (result) {
        const capitalizedType = capitalize(type);
        toast.success(
          isUpdate
            ? `${capitalizedType} updated`
            : `${capitalizedType} created`,
        );
        if (!isUpdate) {
          router.push(
            getSavedPromptLink({
              orgName,
              projectSlug: projectName,
              type,
              promptId: prompt.id,
            }),
          );
        }
      }

      return result;
    } catch (error) {
      toast.error(`Failed to save ${type}`, {
        description: `${error}`,
      });
      return null;
    }
  };

  const onDuplicate = async (prompt: UIFunction) => {
    if (!projectId || !org.id) {
      throw new Error("Missing project or org context");
    }

    try {
      const newPromptId = newId();
      const name = `${prompt.name} (copy)`;
      const slug = slugify(name);
      const result = await createOrUpdatePrompt({
        dml,
        orgId: org.id,
        update: false,
        updateSlug: false,
        prompt: {
          ...prompt,
          project_id: projectId,
          id: newPromptId,
          name,
          slug,
          _xact_id: "0",
        },
      });

      if (result) {
        toast.success(`${capitalize(type)} duplicated`, {
          action: {
            label: `Go to ${type}`,
            onClick: () => {
              router.push(
                getSavedPromptLink({
                  orgName,
                  projectSlug: projectName,
                  promptId: newPromptId,
                  type,
                }),
              );
            },
          },
        });
      }

      return result;
    } catch (error) {
      toast.error(`Failed to save ${type}`, {
        description: `${error}`,
      });
    }
  };

  const onDelete = () => {
    setIsDeleting(true);
  };

  const onConfirmDelete = async () => {
    if (!org.id || !projectId) {
      throw new Error("Missing project or org context");
    }

    const href = `${getProjectLink({ orgName, projectName })}/${type}s`;

    try {
      router.prefetch(href);
      await deleteRow(dml, coercedFunction);
    } catch (e) {
      toast.error(`Failed to delete ${type}`, {
        description: `${e}`,
      });
    } finally {
      router.push(href);
    }
  };

  return (
    <ProjectPromptsDropdownProvider>
      <ScorersDropdownProvider>
        <MainContentWrapper
          className={cn(
            "flex flex-col overflow-hidden py-0 flex-1 bg-primary-50",
            HEIGHT_WITH_TOP_OFFSET,
          )}
          hideFooter
        >
          <SyncedPromptsProvider
            allAvailableModels={allAvailableModels}
            externalValue={promptState}
          >
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap items-center gap-2">
                {type === "scorer" && (
                  <Percent className="size-4 text-lime-600 dark:text-lime-400" />
                )}
                {type === "prompt" && (
                  <MessageCircle className="size-4 text-cyan-600 dark:text-cyan-400" />
                )}
                <h1 className="py-2 font-semibold">
                  {func?.name ?? `Create ${type}`}
                </h1>
                {func && func._xact_id != "0" && (
                  <>
                    <CopyToClipboardButton
                      size="xs"
                      variant="ghost"
                      copyMessage="Copy ID to clipboard"
                      textToCopy={func.id}
                      className="hidden font-mono text-xs font-medium text-primary-500 lg:flex"
                    >
                      <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
                        ID
                      </span>
                      {func.id}
                    </CopyToClipboardButton>
                    <CopyToClipboardButton
                      size="xs"
                      variant="ghost"
                      copyMessage="Copy version to clipboard"
                      textToCopy={func._xact_id}
                      className="-mx-2 hidden font-mono text-xs font-medium text-primary-500 lg:flex"
                    >
                      <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
                        Version
                      </span>
                      {prettifyXact(func._xact_id)}
                    </CopyToClipboardButton>
                  </>
                )}
              </div>
              <FunctionDetailActionButtons
                mode={{
                  type: modeType,
                  upsert,
                  onDuplicate: isUpdate ? onDuplicate : undefined,
                  onDelete: isUpdate ? onDelete : undefined,
                }}
                type={type}
                getOutputPrompt={getOutputPrompt}
                isSlugTouchedRef={isSlugTouchedRef}
                error={error}
                setError={setError}
                setIsDirty={setIsDirty}
                hasLintErrors={false} // TODO
                initialDirtyFunctionComparisonBase={coercedFunction}
                orgName={orgName}
                projectName={projectName}
                projectId={projectId}
                dataEditorValue={runData}
                extraMessagesPath={extraMessagesPath}
              >
                {modeType !== "create" && (
                  <CreateExperimentDialog
                    mode="single"
                    scorers={defaultScorers}
                    scorerFunctions={scorerFunctions}
                    maxConcurrency={10}
                    strict={false}
                    getFunctions={async () => {
                      const block = promptState[0];
                      if (
                        modeType !== "update" ||
                        coercedFunction.function_type === "scorer" ||
                        !block
                      ) {
                        return [];
                      }

                      const output = getOutputPrompt(block);
                      const task: CreateExperimentTask = {
                        function_id: output.id,
                        version: output._xact_id,
                        versionData: {
                          id: output.id,
                          projectId,
                          objectType: "project_prompts",
                          version: output._xact_id,
                        },
                        name: output.name ?? "Experiment",
                        metadata: {},
                      };
                      return [task];
                    }}
                  >
                    <Button size="xs" variant="ghost" Icon={Plus}>
                      Experiment
                    </Button>
                  </CreateExperimentDialog>
                )}
              </FunctionDetailActionButtons>
            </div>
            <BodyWrapper
              isSidenavDocked={isSidenavDocked}
              outerClassName={cn("-mx-3", HEIGHT_WITH_DOUBLE_TOP_OFFSET)}
              innerClassName="flex-row flex-1 overflow-hidden flex"
            >
              <div className="flex flex-1 flex-col lg:max-w-screen-sm">
                <FunctionDetailEditor
                  func={coercedFunction}
                  type={type}
                  modeType={modeType}
                  metaFields={metaFields}
                  setMetaFields={setMetaFields}
                  isSlugTouchedRef={isSlugTouchedRef}
                  setError={setError}
                />
              </div>
              <div className="hidden flex-1 flex-col border-l lg:flex">
                <Tabs
                  value={tab}
                  onValueChange={(tab) =>
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                    setTab(tab as "run" | "chat" | "activity")
                  }
                  className="flex h-full flex-1 flex-col"
                >
                  <div className="flex flex-none overflow-x-auto border-b px-3 py-2 border-primary-200/80">
                    <TabsList className="flex gap-2">
                      {availableTabs?.includes("run") && (
                        <TabsTrigger value="run" asChild>
                          <Button
                            size="xs"
                            variant={tab === "run" ? "default" : "ghost"}
                            className="px-3"
                            Icon={Play}
                          >
                            Run
                          </Button>
                        </TabsTrigger>
                      )}
                      {availableTabs?.includes("chat") && (
                        <TabsTrigger value="chat" asChild>
                          <Button
                            size="xs"
                            variant={tab === "chat" ? "default" : "ghost"}
                            className="px-3"
                            Icon={MessagesSquare}
                          >
                            Chat
                          </Button>
                        </TabsTrigger>
                      )}
                      {isUpdate && availableTabs?.includes("activity") && (
                        <TabsTrigger value="activity" asChild>
                          <Button
                            size="xs"
                            variant={tab === "activity" ? "default" : "ghost"}
                            className="px-3"
                            Icon={GalleryVerticalEnd}
                          >
                            Activity
                          </Button>
                        </TabsTrigger>
                      )}
                    </TabsList>
                  </div>
                  <div className="relative flex min-h-0 flex-1">
                    {availableTabs?.includes("run") && (
                      <TabsContent
                        value="run"
                        className={cn("hidden", tab === "run" && "flex flex-1")}
                        forceMount
                      >
                        <FunctionDetailRun
                          getOutputPrompt={getOutputPrompt}
                          type={type}
                          projectId={projectId}
                          orgName={orgName}
                          isUpdate={isUpdate}
                          func={coercedFunction}
                        />
                      </TabsContent>
                    )}
                    {availableTabs?.includes("chat") && (
                      <TabsContent
                        value="chat"
                        className={cn(
                          "hidden",
                          tab === "chat" && "flex flex-1 justify-center",
                        )}
                        forceMount
                      >
                        <PromptChat
                          id={coercedFunction.id}
                          runData={runData}
                          setRunData={setRunData}
                          extraMessagesPath={extraMessagesPath}
                          setExtraMessagesPath={setExtraMessagesPath}
                        />
                      </TabsContent>
                    )}
                    {isUpdate && availableTabs?.includes("activity") && (
                      <TabsContent
                        value="activity"
                        className={cn(
                          "hidden",
                          tab === "activity" && "flex flex-1",
                        )}
                        forceMount
                      >
                        <FunctionDetailActivity
                          isActive={tab === "activity"}
                          func={coercedFunction}
                          getOutputPrompt={getOutputPrompt}
                          objectType={objectType}
                          type={type}
                          auditLogReady={auditLogReady}
                          auditLogScan={auditLogScan}
                          commentOn={dml.commentOn}
                          deleteComment={dml.deleteComment}
                          modeType={modeType}
                          projectName={projectName}
                          setMetaFields={setMetaFields}
                        />
                      </TabsContent>
                    )}
                  </div>
                </Tabs>
              </div>
              <ConfirmationDialog
                open={isDeleting}
                onOpenChange={() => setIsDeleting(false)}
                title={`Delete ${type}`}
                description={
                  <span>
                    Are you sure you want to delete{" "}
                    <span className="font-medium">{coercedFunction?.name}</span>
                    ?
                  </span>
                }
                confirmText="Delete"
                onConfirm={onConfirmDelete}
                keepOpenOnConfirm
              />
              <FunctionDetailConfirmSaveDialog
                isDirty={isDirty}
                getOutputPrompt={getOutputPrompt}
                upsert={upsert}
                isSlugTouchedRef={isSlugTouchedRef}
              />
            </BodyWrapper>
          </SyncedPromptsProvider>
        </MainContentWrapper>
      </ScorersDropdownProvider>
    </ProjectPromptsDropdownProvider>
  );
}
