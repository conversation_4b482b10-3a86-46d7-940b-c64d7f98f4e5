import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { Button } from "#/ui/button";
import {
  type Message,
  type FunctionObjectType,
} from "@braintrust/core/typespecs";
import { ArrowUpRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { isDatasetReservedName } from "#/ui/prompts/hooks";
import { capitalize, isObject } from "@braintrust/core";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { useOrg } from "#/utils/user";
import { useMemo } from "react";
import { useSetAtom } from "jotai";
import { draftFunctionAtom } from "#/ui/prompts/atoms";
import {
  type UIFunction,
  type SyncedPlaygroundBlock,
} from "#/ui/prompts/schema";
import { TransactionIdField } from "@braintrust/local/query";
import { BasicTooltip } from "#/ui/tooltip";

export function FunctionDialogDetailLink({
  dataEditorValue,
  isSaved,
  orgName,
  projectName,
  type,
  extraMessages,
  prompt,
  getOutputPrompt,
  promptProjectId,
}: {
  dataEditorValue: Record<string, unknown>;
  isSaved: boolean;
  orgName: string;
  projectName: string;
  type: FunctionObjectType;
  extraMessages?: Message[];
  prompt: SyncedPlaygroundBlock;
  promptProjectId?: string;
  getOutputPrompt: (prompt: SyncedPlaygroundBlock) => UIFunction;
}) {
  const org = useOrg();

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: org.name,
    },
  });

  const promptProjectName = useMemo(() => {
    if (!promptProjectId || !projects) {
      return projectName;
    }
    const project = projects.find((p) => p.project_id === promptProjectId);
    return project?.project_name ?? projectName;
  }, [promptProjectId, projects, projectName]);

  const entityIdentifier = isSaved ? prompt.id : "new";
  const [_runData, setRunData] = useEntityStorage({
    entityType: "functions",
    entityIdentifier,
    key: "functionDetailRunData",
  });
  const [_extraMessagesPath, setExtraMessagesPath] = useEntityStorage({
    entityType: "functions",
    entityIdentifier,
    key: "functionDetailExtraMessagesPath",
  });

  const router = useRouter();
  const setDraftFunction = useSetAtom(draftFunctionAtom);

  return (
    <BasicTooltip tooltipContent={`Open ${type} in full editor`}>
      <Button
        Icon={ArrowUpRight}
        size="xs"
        variant="ghost"
        onClick={() => {
          const input = isObject(dataEditorValue.input)
            ? dataEditorValue.input
            : {};
          const expected = dataEditorValue.expected;
          const metadata = isObject(dataEditorValue.metadata)
            ? dataEditorValue.metadata
            : undefined;

          const rest: Record<string, unknown> = {};
          for (const [key, value] of Object.entries(dataEditorValue)) {
            if (!isDatasetReservedName(key)) {
              rest[key] = value;
            }
          }

          const runData = {
            input: {
              ...input,
              ...(extraMessages ? { appendedMessages: extraMessages } : {}),
              ...(Object.keys(rest).length > 0 ? rest : {}),
            },
            expected,
            metadata,
          };

          setRunData(runData);
          if (extraMessages) {
            setExtraMessagesPath("input.appendedMessages");
          }

          // For unsaved functions, store the current function in the draft atom,
          // so that it can be used to initialize the function detail view
          if (!isSaved) {
            setDraftFunction(getOutputPrompt(prompt));
          }

          router.push(
            getSavedPromptLink({
              orgName,
              projectSlug: promptProjectName,
              type,
              promptId: entityIdentifier,
              promptVersion: isSaved ? prompt[TransactionIdField] : undefined,
              editFromPromptVersion: isSaved,
            }),
          );
        }}
      >
        {capitalize(type)} page
      </Button>
    </BasicTooltip>
  );
}
