import {
  type UIFunction,
  type PromptData,
  type SyncedPlaygroundBlock,
} from "#/ui/prompts/schema";
import {
  type InitialPromptArgs,
  createPromptSession,
} from "#/app/app/[org]/prompt/[prompt]/createPromptSession";
import { getPlaygroundLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useRouter } from "next/navigation";
import { useOrg, useUser } from "#/utils/user";
import { useCallback } from "react";
import { createDataset } from "#/app/app/[org]/p/[project]/datasets/[dataset]/createDataset";
import { newId } from "#/utils/btapi/btapi";
import {
  DatasetIdField,
  performUpsert,
  TransactionIdField,
  LogIdField,
} from "#/utils/duckdb";
import { IS_MERGE_FIELD } from "@braintrust/core";
import { isEmpty } from "#/utils/object";
import { useSessionToken } from "#/utils/auth/session-token";
import { type PromptSessionData } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/use-prompt-session-data";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { Button } from "#/ui/button";
import { newObjectName } from "#/utils/metadata";
import { Plus } from "lucide-react";
import { type Mode } from "./types";
import { insertSlug, deletePrompt } from "#/ui/prompts/prompt-client-actions";
import { PROMPT_LOG_ID } from "#/utils/schema";
import { BasicTooltip } from "#/ui/tooltip";

export function CreatePlaygroundButton({
  orgName,
  projectName,
  type,
  datasetRows,
  prompt,
  promptName,
  projectId,
  mode,
  getOutputPrompt,
  extraMessagesPath,
}: {
  orgName: string;
  projectName: string;
  type: FunctionObjectType;
  datasetRows?: DummyDataRow[];
  prompt: SyncedPlaygroundBlock;
  promptName?: string;
  projectId: string;
  mode: Mode;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  extraMessagesPath?: string;
}) {
  const { createNewPlayground } = useCreateNewPlayground({
    orgName,
    projectName,
  });

  return (
    <BasicTooltip tooltipContent={`Create a new playground with this ${type}`}>
      <Button
        Icon={Plus}
        size="xs"
        variant="ghost"
        onClick={async () => {
          // If we're creating a playground while viewing an unsaved scorer, we need to upsert the scorer first
          if (mode.type === "view_unsaved" && type === "scorer") {
            await mode.upsert(getOutputPrompt(prompt));
          }

          const promptData = {
            ...(type === "prompt"
              ? {
                  ...prompt.prompt_data,
                  ...(mode.type === "view_saved" || mode.type === "update"
                    ? {
                        origin: {
                          prompt_id: prompt.id,
                          project_id: projectId,
                          prompt_version: prompt[TransactionIdField],
                        },
                      }
                    : {}),
                }
              : {}),
            ...(type === "tool"
              ? {
                  tool_functions: [
                    { id: prompt.id, type: "function" as const },
                  ],
                }
              : {}),
          };
          const initialRecords =
            Object.keys(promptData).length > 0
              ? [
                  {
                    prompt_data: promptData,
                  },
                ]
              : undefined;

          createNewPlayground({
            sessionName: newObjectName(`Opened-${promptName ?? type}`),
            initialRecords,
            datasetRows,
            promptSessionData: {
              ...(extraMessagesPath
                ? { extraMessages: extraMessagesPath }
                : {}),
              scorers:
                type === "scorer" ? [{ id: prompt.id, type: "function" }] : [],
            },
          });
        }}
      >
        Playground
      </Button>
    </BasicTooltip>
  );
}

export function useCreateNewPlayground({
  orgName,
  projectName,
  onFinish,
  routerMethod = "push",
  orgId: orgIdProp,
}: {
  orgName: string;
  projectName: string;
  onFinish?: () => void;
  routerMethod?: "push" | "replace";
  orgId?: string;
}) {
  const router = useRouter();

  const { user } = useUser();
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const apiUrl = org.api_url;
  const userId = user?.id;

  const defaultPromptData = useDefaultPromptData();

  const createNewPlayground = useCallback(
    async ({
      sessionName,
      datasetName,
      initialRecords: initialRecordsArgs,
      promptSessionData,
      datasetRows,
      scorerFunctions,
      projectId,
    }: {
      sessionName: string;
      datasetName?: string;
      initialRecords?: {
        prompt_data: PromptData;
        function_data?: UIFunction["function_data"];
      }[];
      promptSessionData?: PromptSessionData;
      datasetRows?: DummyDataRow[];
      scorerFunctions?: Record<string, UIFunction>;
      projectId?: string;
    }) => {
      const orgId = orgIdProp ?? org.id;

      if (!userId || !orgId) {
        throw new Error("Unauthorized: user id or token missing");
      }

      const initialPromptArgs = {
        apiUrl,
        getOrRefreshToken,
        userId,
      };

      const [promptMetadata, datasetMetadata, _scorerMetadata] =
        await Promise.all([
          (async () => {
            const initialRecords = initialRecordsArgs ?? [
              {
                prompt_data: defaultPromptData,
              },
            ];
            const { data, error } = await createPromptSession({
              orgName,
              projectName,
              sessionName,
              initialRecords,
              initialPromptArgs,
            });
            if (error) {
              throw new Error(error);
            }
            return data;
          })(),
          datasetRows
            ? createDummyData({
                orgId,
                projectName,
                datasetName: datasetName ?? `For playground ${sessionName}`,
                rows: datasetRows,
                initialPromptArgs,
              })
            : (async () => null)(),
          scorerFunctions && projectId
            ? createScorers({
                orgId,
                projectId,
                scorerFunctions,
                initialPromptArgs,
              })
            : (async () => null)(),
        ]);

      const sessionToken = await getOrRefreshToken();
      await performUpsert(
        null,
        initialPromptArgs.apiUrl,
        sessionToken,
        initialPromptArgs.userId,
        [
          {
            id: newId(),
            prompt_session_id: promptMetadata.id,
            org_id: org.id,
            prompt_session_data: {
              ...promptSessionData,
              dataset_id: datasetMetadata?.id,
            },
          },
        ],
      );

      onFinish?.();

      router[routerMethod](
        getPlaygroundLink({
          orgName,
          projectName,
          playgroundName: promptMetadata.name,
        }),
      );
    },
    [
      orgIdProp,
      org.id,
      userId,
      apiUrl,
      getOrRefreshToken,
      projectName,
      onFinish,
      router,
      routerMethod,
      orgName,
      defaultPromptData,
    ],
  );

  return {
    createNewPlayground,
  };
}

export interface DummyDataRow {
  input?: unknown;
  metadata?: Record<string, unknown>;
  expected?: unknown;
}

async function createDummyData({
  orgId,
  projectName,
  datasetName,
  rows,
  initialPromptArgs,
}: {
  orgId: string;
  projectName: string;
  datasetName: string;
  rows: DummyDataRow[];
  initialPromptArgs: InitialPromptArgs;
}) {
  const { data: metadata } = await createDataset({
    orgId,
    projectName,
    datasetName,
  });
  if (!metadata.dataset?.id) {
    throw new Error("Failed to create dummy dataset");
  }

  // To avoid creating a DML hook, we just run the correct
  // `performUpsert` command directly
  const dataRows = rows.map(
    (r) => ({
      id: newId(),
      [DatasetIdField]: metadata.dataset.id,
      ...r,
      [IS_MERGE_FIELD]: false,
    }),
    [],
  );

  const sessionToken = await initialPromptArgs.getOrRefreshToken();
  await performUpsert(
    null,
    initialPromptArgs.apiUrl,
    sessionToken,
    initialPromptArgs.userId,
    dataRows,
  );

  return metadata.dataset;
}

async function createScorers({
  orgId,
  projectId,
  scorerFunctions,
  initialPromptArgs,
}: {
  orgId: string;
  projectId: string;
  scorerFunctions: Record<string, UIFunction>;
  initialPromptArgs: InitialPromptArgs;
}) {
  const scorerValues = Object.values(scorerFunctions);
  if (scorerValues.length === 0) {
    return null;
  }

  // Reserve slugs for all scorers first
  const slugResults = await Promise.all(
    scorerValues.map(async (scorer) => {
      if (isEmpty(scorer.name) || scorer.name.trim() === "") {
        throw new Error("Scorer name cannot be empty");
      }
      if (isEmpty(scorer.slug) || scorer.slug.trim() === "") {
        throw new Error("Scorer slug cannot be empty");
      }

      const { prompt_id: insertedPromptId, found_existing } = await insertSlug({
        orgId,
        projectId: projectId,
        promptId: scorer.id,
        slug: scorer.slug,
      });
      if (found_existing) {
        throw new Error(`Slug '${scorer.slug}' already exists`);
      }
      return { scorer, insertedPromptId };
    }),
  );

  try {
    // Prepare all scorers for upsert
    const preparedScorers = scorerValues.map((scorer) => {
      // Remove transaction id and add required fields
      const { [TransactionIdField]: _, ...scorerNoXact } = scorer;
      const preparedScorer = {
        ...scorerNoXact,
        project_id: projectId,
        [LogIdField]: PROMPT_LOG_ID,
        [IS_MERGE_FIELD]: false,
      };

      // Remove prompt_data if it's not a prompt type
      if (preparedScorer.function_data.type !== "prompt") {
        delete preparedScorer.prompt_data;
      }

      return preparedScorer;
    });

    // Perform batch upsert
    const sessionToken = await initialPromptArgs.getOrRefreshToken();
    await performUpsert(
      null,
      initialPromptArgs.apiUrl,
      sessionToken,
      initialPromptArgs.userId,
      preparedScorers,
    );

    return { success: true, scorerIds: Object.keys(scorerFunctions) };
  } catch (e) {
    // If upsert failed, clean up the reserved slugs
    await Promise.all(
      slugResults.map(async ({ insertedPromptId }) => {
        try {
          await deletePrompt({ id: insertedPromptId });
        } catch (innerE) {
          console.error(
            "Failed to delete prompt after backend insert failed\n",
            innerE,
          );
        }
      }),
    );
    throw e;
  }
}
